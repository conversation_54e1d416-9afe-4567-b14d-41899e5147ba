% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/semiquantification.R
\name{normalize_area}
\alias{normalize_area}
\title{Normalize Area by D8 Internal Standard}
\usage{
normalize_area(df, start_col = 12)
}
\arguments{
\item{df}{A data frame containing the data to be normalized.}

\item{start_col}{The starting column index from which concentration columns begin. Default is 12.}
}
\value{
A data frame with normalized areas and without D8 or blank samples.
}
\description{
This function normalizes the area of compounds in a data frame by the area of the internal
standard (D8). It processes each batch separately and removes rows and columns related to D8 or
blank samples.
}
