% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/semiquantification.R
\name{keep_area}
\alias{keep_area}
\title{Keep specified peak areas in the dataframe}
\usage{
keep_area(df, sam_code, start_col = 12, keep_bk = TRUE, keep_d8 = TRUE)
}
\arguments{
\item{df}{A dataframe containing the data to be processed.}

\item{sam_code}{A dataframe containing sample codes and their corresponding names.}

\item{start_col}{The starting column index of peak area columns (default is 12).}

\item{keep_bk}{A logical indicating whether to keep 'BK' in the list of samples (default is
TRUE).}

\item{keep_d8}{A logical indicating whether to keep 'D8' in the list of samples (default is
TRUE).}
}
\value{
A dataframe with the specified areas kept and others set to NA.
}
\description{
This function processes a dataframe to keep only the specified peak areas based on sample codes
in the Comment column and optional flags for 'BK' and 'D8'.
}
