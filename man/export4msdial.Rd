% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/export_databases.R
\name{export4msdial}
\alias{export4msdial}
\title{Export database for MS-DIAL}
\usage{
export4msdial(data, polarity = "pos", output = "database for msdial.txt")
}
\arguments{
\item{data}{The list of chemicals should contain at least Name and ExactMass}

\item{polarity}{The ESI polarity applied in your dat. Either "pos" or "neg".}

\item{output}{The path where the structure database will be stored. It must be
in txt format. For example, "c:/data/database for msdial.txt"}
}
\description{
\code{export4msdial} provides a way to convert any list of chemicals into a
structure database that can be used by MS-DIAL for post-identification.
please refer to \link{extract_cid} and \link{extract_meta} regarding meta
data extraction.
}
