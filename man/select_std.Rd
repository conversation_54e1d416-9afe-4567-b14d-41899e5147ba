% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/semiquantification.R
\name{select_std}
\alias{select_std}
\title{Select Standard}
\usage{
select_std(
  std_md,
  std_res_col,
  std_md1_col,
  data_md,
  data_md1_col,
  top_npct_md = 20
)
}
\arguments{
\item{std_md}{The data for the standards including at least the Name,
response variable for each standard (can be either slope of the calibration
curve, peak area, or any other response variable), and molecular descriptor
for each standard. The molecular descriptor data should be at the end of the
dataframe}

\item{std_res_col}{The column index of the response variable in std_md.
If you want to use all molecular descriptors, set std_res_col to FALSE}

\item{std_md1_col}{The column index where the molecular descriptor starts
in std_md}

\item{data_md}{The data to be determined including at least the Name and
molecular descriptor for each standard. The molecular descriptor data should
be at the end of the dataframe}

\item{data_md1_col}{The column index where the molecular descriptor data
starts in data_md}

\item{top_npct_md}{The percentage of top molecular descriptors to select
(default is 20)}
}
\value{
A metadata dataframe with the selected standard assigned to each
substance and their similarity value
}
\description{
This function selects the standard used for semi-quantification of a given
set of substances based on chemical structure similarity calculations between
the standards avaiable and the target substances
}
