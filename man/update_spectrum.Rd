% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/plot_spectrum.R
\name{update_spectrum}
\alias{update_spectrum}
\title{Convert spectral string to a named numeric intensity vector}
\usage{
update_spectrum(
  spectrum_str,
  start_mz = 50,
  end_mz = 500,
  mz_step = 1,
  digits = 0
)
}
\arguments{
\item{spectrum_str}{A character string representing the spectrum,
formatted as "m/z1:intensity1 m/z2:intensity2 ...".}

\item{start_mz}{Numeric. Starting m/z value of the output vector. Default is 50.}

\item{end_mz}{Numeric. Ending m/z value of the output vector. Default is 500.}

\item{mz_step}{Numeric. Step size (bin width) for m/z axis. Default is 1.}

\item{digits}{Integer. Number of decimal places to which m/z values are rounded. Default is 0.}
}
\value{
A named numeric vector of intensities, where names are m/z values (as characters).
}
\description{
This function parses a space-separated string of m/z:intensity pairs (e.g., "100:30 120:90")
into a named numeric vector representing a binned and normalized mass spectrum.
It forms the foundation for downstream spectral similarity and visualization tasks.
}
\examples{
update_spectrum("98.7:10 99.9:20 101.2:15", start_mz = 98, end_mz = 102)

}
