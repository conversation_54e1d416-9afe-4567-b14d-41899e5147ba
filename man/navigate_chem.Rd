% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/shinyApp_plotMolecule.R
\name{navigate_chem}
\alias{navigate_chem}
\title{A shinyApp to navigate through a chemical table}
\usage{
navigate_chem(data)
}
\arguments{
\item{data}{A dataframe contain at least SMILES of the chemical}
}
\description{
\code{navigate_chem} is a shinyApp to navigate through a chemical table and
to see the structure of each one. The data must contain SMILES of each chemical.
}
