% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/shinyApp_plotMolecule.R
\name{plot_molecule}
\alias{plot_molecule}
\title{An internal function to plot molecule}
\usage{
plot_molecule(molecule, name = NULL, sma = NULL, ...)
}
\arguments{
\item{molecule}{an object as returned by rcdk::load.molecules or rcdk::parse.smiles}

\item{name}{a character for the name of the molecule}

\item{sma}{a character with the smarts string as passed onto get.depictor}

\item{...}{other arguments for get.depictor}
}
\value{
an image showing the chemical structure
}
\description{
\code{plot_molecule} is a simple wrapper to plot chemical structure based on
SMILES.
}
