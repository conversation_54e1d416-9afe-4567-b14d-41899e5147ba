% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/plot_spectrum.R
\name{plot_spectrum}
\alias{plot_spectrum}
\title{Plot a single mass spectrum}
\usage{
plot_spectrum(spectrum, range = 10, threshold = 1, max_ticks = 20)
}
\arguments{
\item{spectrum}{A named numeric vector representing the spectrum (m/z as names, intensity as values).}

\item{range}{Integer. The bin width (in m/z) used to identify local peak maxima for labeling. Default is 10.}

\item{threshold}{Numeric. The minimum intensity (in \%) a peak must have to be labeled. Default is 1.}

\item{max_ticks}{Integer. Maximum number of x-axis tick marks. Default is 20.}
}
\value{
An interactive plotly plot showing the mass spectrum.
}
\description{
Creates an interactive bar plot of a single spectrum using ggplot2 and plotly.
Peaks are normalized to 100\% intensity. The highest peak in each m/z bin is optionally labeled.
}
\examples{
spectrum <- update_spectrum("100:30 120:80 145:60 170:90 200:20")
plot_spectrum(spectrum)

}
