% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/semiquantification.R
\name{organize_con}
\alias{organize_con}
\title{Organize and summarize data after semi0quantification}
\usage{
organize_con(
  df,
  sam_code,
  start_col = 12,
  digits = 2,
  na2zero = TRUE,
  bind_mean_sd = TRUE
)
}
\arguments{
\item{df}{A dataframe after being processed by the 'calculate_con' function.}

\item{sam_code}{A dataframe containing sample codes and their corresponding names.}

\item{start_col}{The starting column index of peak area columns (default is 12).}

\item{digits}{An integer specifying the number of decimal places to round the results (default
is 2).}

\item{na2zero}{A logical value indicating whether to replace NA values with zero (default is
TRUE).}

\item{bind_mean_sd}{A logical value indicating whether to bind mean and standard deviation into
a single column for each sample (default is TRUE).}
}
\value{
A dataframe with summarized and organized data.
}
\description{
This function takes a dataframe and a sample code dataframe to organize and summarize data after
semi-quantification. It calculates minimum, median, maximum, and overall mean for each sample.
Optionally, you can choose to replace NA values with zero or not. Moreover, you can also choose
wether to bind mean and standard deviation into a single column for each sample or not.
}
