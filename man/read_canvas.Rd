% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/process_canvas_data.R
\name{read_canvas}
\alias{read_canvas}
\title{Manually align Canvas data}
\usage{
read_canvas(
  path,
  ri_iden_tol = 30,
  ri_p_iden_tol = 100,
  ri_align_tol = 50,
  rt_2d_align_tol = 0.1,
  keep = "area"
)
}
\arguments{
\item{path}{Path of the folder contains .*txt files originally exported from
Canvas}

\item{ri_iden_tol}{RI tolerance acceptable for identification when experimental
reference RI is available, normally 30}

\item{ri_p_iden_tol}{RI tolerance acceptable for identification when experimental
reference RI is not available, and predicted one is used, normally 100}

\item{ri_align_tol}{RI tolerance acceptable for alignment}

\item{rt_2d_align_tol}{2nd dimension RT acceptable for alignment}

\item{keep}{whether 'area', 'height', or 'both', telling which information to
be kept. If keep = 'area', then only peak areas of the compounds in detected
samples will be kept; if keep = 'height', then only peak height of the compounds
in detected samples will be kept; if keep = 'both', then both peak areas and
peak height of the compounds in detected samples will be kept.}
}
\value{
A \code{data.frame} with compound name, average RT, RI, 2d RT, CAS, as
well as peak are/height in each sample.
}
\description{
\code{read_canvas} Facilitate the alignment of canvas data by directly
reading .*txt files export from Canvas. Canvas is currently only capable of
processing data one by one, which is error-prone and tedious to align and
combine data from different samples. This function
aims to facilitate this process and help us find conflicting identification
among different samples. The first step is to manually identify and mark peaks
of interest in Canvas. Then, exported the marked peaks as .txt from all samples
into a folder. The function will then read all .txt files and combine them into
a single data by matching the chemical name. Based on the defined tolerance of
retention index and 2nd dimension retention time, the function will evaluate
every chemical compound and then prompt up message about in which samples the
compounds are detected and which samples have the maximum and minimum RI, with
the aim to help identify which samples could have mis-identification results.
In order to be reproducible and tracible, mis-identification results should be
modified in Canvas and re-exported into .txt replacing the previous ones.
Manual modification of the .txt or the combined data is not recommended.
}
