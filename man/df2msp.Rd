% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/msp_conversion.R
\name{df2msp}
\alias{df2msp}
\title{Convert dataframe to MSP format}
\usage{
df2msp(df, output_file)
}
\arguments{
\item{df}{Dataframe containing the MSP data}

\item{output_file}{Path to the output MSP file}
}
\description{
This function converts a dataframe back to MSP (Mass Spectra Project) format and writes it to a file.
}
\examples{
\dontrun{
df2msp(my_data, "output.msp")
}
}
