% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/msp_conversion.R
\name{msp2df}
\alias{msp2df}
\title{Convert MSP file to dataframe}
\usage{
msp2df(msp_file)
}
\arguments{
\item{msp_file}{Path to the MSP file to be converted}
}
\value{
A dataframe containing the MSP data with columns for each field and a Spectrum column for peak data
}
\description{
This function reads an MSP (Mass Spectra Project) file and converts it into a dataframe format.
}
\examples{
\dontrun{
df <- msp2df("example.msp")
}
}
