% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/metadata_extraction.R
\name{extract_cid}
\alias{extract_cid}
\title{Extract compound identification number (CID) from Pubchem}
\usage{
extract_cid(
  data,
  name_col = FALSE,
  cas_col = FALSE,
  inchikey_col = FALSE,
  verbose = TRUE
)
}
\arguments{
\item{data}{A data.frame or tibble contains at least CAS, Chemical name, or
InChIKey column.}

\item{name_col}{The index of column that contains chemical name.}

\item{cas_col}{The index of column that contains CAS information. CAS number
is not mandatory for each compound, if no CAS is available, then chemical name
will be used for retrieval.}

\item{inchikey_col}{The index of column that contains InChIKey. It is optional.}

\item{verbose}{Whether or not to show verbose information during extraction}
}
\value{
A data.frame or tibble with a CID column added.
}
\description{
\code{extract_cid()} is a simple wrapper of the \code{get_cid()} function from
the \emph{webchem} package. It extracts \strong{cid} based on the keys, i.e.,
\strong{InChIKey}, \strong{CAS} and \strong{chemical names}. You can use
any or all of these keys. In the latter case, it will first use InChIKey,
followed by CAS and chemical name if no CID is available from the previous step.
When multiple matches returns, only the first one will be kept. Importantly,
it appends these information to your original data.frame, which is more friendly
for new R users. Only English chemical name is accepted.
}
\examples{
# without InChIKey
x <- data.frame(CAS = "128-37-0", Name = "BHT")
x_cid <- extract_cid(x, cas_col = 1, name_col = 2)

# with InChIKey
x <- data.frame(CAS = "128-37-0", Name = "BHT", InChIKey = "NLZUEZXRPGMBCV-UHFFFAOYSA-N")
x_cid <- extract_cid(x, cas_col = 1, name_col = 2, inchikey_col = 3)
}
