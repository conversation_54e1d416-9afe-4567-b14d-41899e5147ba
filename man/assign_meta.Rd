% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/metadata_extraction.R
\name{assign_meta}
\alias{assign_meta}
\title{Assign meta data (at the moment only SMILES) to the data}
\usage{
assign_meta(data, meta_file)
}
\arguments{
\item{data}{Your data after \code{extract_meta()}.}

\item{meta_file}{A *.txt file containg at least Name and SMILES, which can
be manully prepared or converted from *MOL files by the \code{combine_mol2sdf()}
and \code{extract_structure()} functions from the \code{mspcompiler} package
\url{https://github.com/QizhiSu/mspcompiler}.}
}
\value{
A dataframe or tibble with SMILES assigned.
}
\description{
If you have some compounds that are not present in the Pubchem, there
will be no SMILES retrieved for these compounds using \code{extract_meta}.
This function offers a way to assign SMIELS for them. However, a txt file
containing Name and SMILES of these compounds is required. There are two options
to prepare this txt file. One is to prepare it manually, the column names must
be Name and SMILES, respectively(case-insensitive). Another one is to prepare
*.MOL files of these molecules and extract SMIL<PERSON> using the \code{combine_mol2sdf()}
and \code{extract_structure()} functions from the \code{mspcompiler} package
\url{https://github.com/QizhiSu/mspcompiler}. Note that the name in your *.txt
file or *.MOL files have to be consistent with the one you have in your data
as Name is used for matching.
}
