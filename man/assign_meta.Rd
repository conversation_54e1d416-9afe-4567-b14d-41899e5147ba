% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/metadata_extraction.R
\name{assign_meta}
\alias{assign_meta}
\title{Assign structural metadata (SMILES, InChIKey, InChI) to your dataset}
\usage{
assign_meta(data, meta_file)
}
\arguments{
\item{data}{A data.frame or tibble, typically the result after running \code{extract_meta()}.}

\item{meta_file}{A text-based file (e.g., *.csv or *.txt) containing at least "Name" and "SMILES",
and optionally "InChIKey" and "InChI". File can be created manually or generated from MOL files.}
}
\value{
A data.frame or tibble with missing SMILES, InChIKey, and InChI values filled in where available.
}
\description{
If some compounds are not found in PubChem, \code{extract_meta()} will not
retrieve their structural information such as SMILES, InChIKey, or InChI.
This function provides a way to manually assign these values based on name matching.
}
\details{
A metadata file (e.g., CSV or TXT) containing at least compound names and SMILES is required.
Column names in the file are case-insensitive and must include "Name" and "<PERSON><PERSON><PERSON>".
If available, "InChI<PERSON>ey" and "InChI" will also be assigned.

There are two ways to prepare this metadata file:
\enumerate{
\item Manually create a file with at least "Name" and "SMILES" columns.
\item Convert MOL files into a structured table using
\code{combine_mol2sdf()} and \code{extract_structure()} from the
\pkg{mspcompiler} package:
\url{https://github.com/QizhiSu/mspcompiler}.
}

Note: compound names in your metadata file must exactly match those in your main dataset
(case-insensitive, whitespace-trimmed) for successful assignment.
}
