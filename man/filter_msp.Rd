% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/filter_msp.R
\name{filter_msp}
\alias{filter_msp}
\title{Filter an EI library by InChIKey}
\usage{
filter_msp(msp, cmp_list, keep_napd8 = TRUE, output)
}
\arguments{
\item{msp}{Path to the library file in msp format}

\item{cmp_list}{A data.frame contains at least Name and InChIKey to be kept}

\item{keep_napd8}{If TRUE, add Naphthalene-D8 to the list of compounds to keep}

\item{output}{path to write the filtered library to}
}
\description{
\code{filter_msp} provide a simple way to filter an EI library by InChIKey, which can then be use
as a small *.msp file for MS-DIAL. This can be useful to targetedly find specific compounds in
your data.
}
