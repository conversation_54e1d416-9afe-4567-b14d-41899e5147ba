% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/msdial_post_processing.R
\name{calculate_freq}
\alias{calculate_freq}
\title{Calculate the detection frequency and detection rate}
\usage{
calculate_freq(data, num_sample, sep = ",")
}
\arguments{
\item{data}{The data returned by the \code{read_msdial()} function.}

\item{num_sample}{Total number of samples.}

\item{sep}{The separator between samples in the Comment field in MS-DIAL.
For example, if you put "1, 2, 3, 4, 5, 6" in the Comment field in MS-DIAL,
where each number denoted as sample code. Then the separator is ",".}
}
\value{
A table with detection frequency and rate added after Comment column.
}
\description{
The frequency and rate of samples that have a compound detected in total number of
samples. It is calculated in terms of samples excluding replicates.
}
