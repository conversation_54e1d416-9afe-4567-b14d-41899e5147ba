# Generated by roxygen2: do not edit by hand

export(assign_meta)
export(calculate_con)
export(calculate_freq)
export(cosine_similarity)
export(df2msp)
export(export4msdial)
export(export4msfinder)
export(export_smiles_to_mol)
export(extract_cid)
export(extract_classyfire)
export(extract_meta)
export(filter_msp)
export(keep_area)
export(msp2df)
export(navigate_chem)
export(normalize_area)
export(organize_con)
export(plot_spectrum)
export(read_canvas)
export(read_msdial)
export(select_std)
export(update_spectrum)
import(DT)
import(dplyr)
import(mspcompiler)
import(purrr)
import(rcdk, except = "matches")
import(shiny, except = c("dataTableOutput", "renderDataTable"))
import(shinyjs, except = c("show", "runExample"))
importFrom(R.utils,withTimeout)
importFrom(beepr,beep)
importFrom(digest,digest)
importFrom(dplyr,all_of)
importFrom(dplyr,bind_rows)
importFrom(dplyr,filter)
importFrom(dplyr,group_by)
importFrom(dplyr,left_join)
importFrom(dplyr,mutate)
importFrom(dplyr,relocate)
importFrom(dplyr,select)
importFrom(dplyr,ungroup)
importFrom(ggplot2,aes)
importFrom(ggplot2,coord_cartesian)
importFrom(ggplot2,element_line)
importFrom(ggplot2,element_text)
importFrom(ggplot2,geom_bar)
importFrom(ggplot2,geom_hline)
importFrom(ggplot2,geom_text)
importFrom(ggplot2,ggplot)
importFrom(ggplot2,scale_fill_manual)
importFrom(ggplot2,scale_x_continuous)
importFrom(ggplot2,scale_y_continuous)
importFrom(ggplot2,theme)
importFrom(ggplot2,theme_classic)
importFrom(glue,glue)
importFrom(janitor,clean_names)
importFrom(janitor,row_to_names)
importFrom(plotly,ggplotly)
importFrom(proxy,simil)
importFrom(purrr,map_dfc)
importFrom(randomForest,importance)
importFrom(randomForest,randomForest)
importFrom(rcdk,generate.2d.coordinates)
importFrom(rcdk,parse.smiles)
importFrom(rcdk,write.molecules)
importFrom(stats,median)
importFrom(stats,na.omit)
importFrom(stats,setNames)
importFrom(tibble,rowid_to_column)
importFrom(utils,capture.output)
importFrom(utils,read.table)
importFrom(utils,write.table)
importFrom(webchem,fn_percept)
importFrom(webchem,get_cid)
importFrom(webchem,pc_prop)
importFrom(webchem,pc_sect)
importFrom(webchem,pc_synonyms)
