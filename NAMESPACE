# Generated by roxygen2: do not edit by hand

export(assign_meta)
export(calculate_con)
export(calculate_freq)
export(export4msdial)
export(export4msfinder)
export(extract_cid)
export(extract_classyfire)
export(extract_meta)
export(filter_msp)
export(keep_area)
export(navigate_chem)
export(normalize_area)
export(organize_con)
export(read_canvas)
export(read_msdial)
export(select_std)
import(DT)
import(classyfireR)
import(dplyr)
import(mspcompiler)
import(purrr)
import(rcdk, except = "matches")
import(shiny, except = c("dataTableOutput", "renderDataTable"))
import(shinyjs, except = c("show", "runExample"))
import(webchem)
importFrom(R.utils,withTimeout)
importFrom(glue,glue)
importFrom(janitor,clean_names)
importFrom(janitor,row_to_names)
importFrom(proxy,simil)
importFrom(purrr,map_dfc)
importFrom(randomForest,importance)
importFrom(randomForest,randomForest)
importFrom(rio,import)
importFrom(stats,median)
importFrom(stats,na.omit)
importFrom(stats,setNames)
importFrom(tibble,rowid_to_column)
importFrom(utils,capture.output)
importFrom(utils,read.table)
importFrom(utils,write.table)
importFrom(webchem,get_cid)
