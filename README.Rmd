---
output: github_document
---

<!-- README.md is generated from README.Rmd. Please edit that file -->

```{r, include = FALSE}
knitr::opts_chunk$set(
  collapse = TRUE,
  eval = FALSE,
  comment = "#>",
  fig.path = "man/figures/README-",
  out.width = "100%"
)
```

<div align="center">

# 🧪 labtools

<img src="https://img.shields.io/badge/R-276DC3?style=for-the-badge&logo=r&logoColor=white" alt="R" height="30"/>

<!-- Language Toggle -->
<div align="right" style="margin-top: -40px;">
  <a href="#english" style="color: #00d4aa; text-decoration: none; font-weight: bold;">🇺🇸 English</a> |
  <a href="#中文" style="color: #00d4aa; text-decoration: none; font-weight: bold;">🇨🇳 中文</a>
</div>

<!-- Dark Mode Badges -->
<p align="center">
  <img src="https://img.shields.io/github/workflow/status/QizhiSu/labtools/R-CMD-check?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=00d4aa" alt="R-CMD-check"/>
  <img src="https://img.shields.io/badge/License-MIT-00d4aa?style=for-the-badge&labelColor=1a1a1a" alt="License"/>
  <img src="https://img.shields.io/badge/CRAN-not%20yet-ff6b6b?style=for-the-badge&labelColor=1a1a1a" alt="CRAN"/>
  <img src="https://img.shields.io/github/stars/QizhiSu/labtools?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=ffd93d" alt="Stars"/>
  <img src="https://img.shields.io/github/issues/QizhiSu/labtools?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=ff6b6b" alt="Issues"/>
  <img src="https://img.shields.io/github/last-commit/QizhiSu/labtools?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=6c5ce7" alt="Last Commit"/>
</p>

---

<h2 style="color: #00d4aa; text-align: center;">⚗️ Analytical Chemistry Laboratory Data Processing Toolkit</h2>

<p align="center" style="font-size: 18px; color: #a0a0a0; font-style: italic;">
  A comprehensive R package for streamlining analytical chemistry workflows
</p>

</div>

---

## <a id="english"></a>🌟 **Key Features**

| Feature | Description |
|---------|-------------|
| 🔍 **Chemical Metadata Extraction** | Automated retrieval from PubChem database with intelligent retry mechanisms |
| 🧬 **Structure Database Management** | Build and visualize chemical structure databases with advanced filtering |
| 📊 **MS Data Processing** | Export optimized databases for MS-FINDER and MS-DIAL software |
| 🔬 **2D GC-MS Analysis** | Process Canvas exports and combine multi-sample data with precision |
| ⚗️ **Chemical Structure Conversion** | SMILES to MOL file conversion with 2D coordinate generation |
| 📈 **Semi-quantification Tools** | Advanced analytical quantification with machine learning integration |
| 🎯 **Interactive Visualization** | Shiny-based chemical structure navigation and spectrum plotting |

---

## 🚀 **Installation**

### Development Version (Recommended)

```{r installation}
# Install devtools if you haven't already
if (!requireNamespace("devtools", quietly = TRUE)) {
  install.packages("devtools")
}

# Install labtools from GitHub
devtools::install_github("QizhiSu/labtools")
```

### System Requirements

- **R** ≥ 4.0.0
- **Java** ≥ 8 (for rcdk package)
- **Internet connection** (for PubChem API access)

---

## ⚡ **Quick Start**

```{r quick-start}
library(labtools)
library(dplyr)

# 🔍 1. Extract chemical metadata from PubChem
data <- data.frame(
  Name = c("Caffeine", "Aspirin", "Glucose"),
  CAS = c("58-08-2", "50-78-2", "50-99-7")
)

# Extract CIDs and comprehensive metadata
data_with_cid <- extract_cid(data, name_col = "Name", cas_col = "CAS")
data_complete <- extract_meta(data_with_cid, cas = TRUE, flavornet = TRUE)

# 📊 2. Export for MS software
export4msdial(data_complete, polarity = "pos", output = "database_msdial.txt")
export4msfinder(data_complete, output = "database_msfinder.txt")

# ⚗️ 3. Convert SMILES to MOL files
smiles_data <- data.frame(
  ID = c("Caffeine", "Aspirin"),
  SMILES = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O")
)
export_smiles_to_mol(smiles_data, output_dir = "mol_files")

# 🎯 4. Interactive chemical structure browser
navigate_chem(data_complete)  # Opens Shiny app
```

---

## 📚 **Function Examples**

### Chemical Metadata Extraction

```{r metadata-extraction}
library(labtools)

# Extract CIDs from chemical identifiers
compounds <- data.frame(
  Name = c("Caffeine", "Theobromine"),
  CAS = c("58-08-2", "83-67-0")
)

# Extract CIDs with proper error handling
compounds_cid <- extract_cid(compounds,
                            name_col = "Name",
                            cas_col = "CAS",
                            timeout = 300,
                            verbose = TRUE)

# Extract comprehensive metadata
compounds_meta <- extract_meta(compounds_cid,
                              cas = TRUE,
                              flavornet = TRUE,
                              synonyms = TRUE)

# Add chemical classification
compounds_classified <- extract_classyfire(compounds_meta)
```

### Database Export

```{r database-export}
# Export for MS-FINDER
export4msfinder(compounds_classified, output = "msfinder_database.txt")

# Export for MS-DIAL (positive mode)
export4msdial(compounds_classified, polarity = "pos", output = "msdial_pos.txt")

# Export for MS-DIAL (negative mode)
export4msdial(compounds_classified, polarity = "neg", output = "msdial_neg.txt")
```

### SMILES to MOL Conversion

```{r smiles-conversion}
# Prepare SMILES data
smiles_data <- data.frame(
  Compound_ID = c("CAFF_001", "THEO_002"),
  SMILES_String = c(
    "Cn1cnc2c1c(=O)n(c(=O)n2C)C",      # Caffeine
    "Cn1cnc2c1c(=O)[nH]c(=O)n2C"       # Theobromine
  )
)

# Convert to MOL files
result <- export_smiles_to_mol(smiles_data,
                              id_col = "Compound_ID",
                              smiles_col = "SMILES_String",
                              output_dir = "molecular_structures")

# Check conversion results
print(result)
```

### Canvas Data Processing

```{r canvas-processing}
# Process Canvas 2D GC-MS data
canvas_data <- read_canvas(
  path = "path/to/canvas/files",
  ri_align_tol = 50,
  rt_2d_align_tol = 0.1,
  keep = "area"
)

# Normalize by internal standard
normalized_data <- normalize_area(canvas_data, start_col = 12)
```

### Semi-quantification Analysis

```{r semiquant-analysis}
# Select appropriate standards
standards_assigned <- select_std(
  std_md = reference_standards,
  std_res_col = 3,
  std_md1_col = 14,
  data_md = target_compounds,
  data_md1_col = 5,
  top_npct_md = 30
)

# Calculate concentrations
concentrations <- calculate_con(standards_assigned, sample_weights)

# Organize results
final_results <- organize_con(concentrations, sample_codes, digits = 3)
```

### Spectral Analysis

```{r spectral-analysis}
# Convert spectrum string to numeric vector
spectrum_str <- "100:30 120:80 145:60 170:90 200:20"
spectrum <- update_spectrum(spectrum_str)

# Create interactive spectrum plot
plot_spectrum(spectrum, range = 10, threshold = 5)

# Calculate spectral similarity
spec1 <- update_spectrum("100:30 120:80 145:60")
spec2 <- update_spectrum("100:25 120:85 145:55")
similarity <- cosine_similarity(spec1, spec2)
```

### MSP Library Filtering

```{r msp-filtering}
# Filter MSP library based on compound list
compounds_to_keep <- data.frame(
  Name = c("Caffeine", "Aspirin"),
  InChIKey = c("RYYVLZVUVIJVGH-UHFFFAOYSA-N", "BSYNRYMUTXBXSQ-UHFFFAOYSA-N")
)

filter_msp(msp = "NIST_library.msp",
          cmp_list = compounds_to_keep,
          keep_napd8 = TRUE,
          output = "filtered_library.msp")
```

---

## 🔬 **Advanced Workflows**

### Complete Database Creation Workflow

```{r complete-workflow}
# Step 1: Prepare data
chemicals <- data.frame(
  Name = c("Benzene", "Toluene", "Xylene"),
  CAS = c("71-43-2", "108-88-3", "1330-20-7")
)

# Step 2: Extract comprehensive metadata
chemicals_cid <- extract_cid(chemicals, name_col = "Name", cas_col = "CAS")
chemicals_meta <- extract_meta(chemicals_cid, cas = TRUE, synonyms = TRUE)
chemicals_class <- extract_classyfire(chemicals_meta)

# Step 3: Export for different platforms
export4msdial(chemicals_class, polarity = "pos", output = "pos_database.txt")
export4msdial(chemicals_class, polarity = "neg", output = "neg_database.txt")
export4msfinder(chemicals_class, output = "msfinder_database.txt")

# Step 4: Generate structure files
export_smiles_to_mol(chemicals_class, output_dir = "structures")
```

### 2D GC-MS Processing Pipeline

```{r gcms-pipeline}
# Process Canvas data
canvas_data <- read_canvas("canvas_files", keep = "area")

# Extract metadata
canvas_meta <- extract_cid(canvas_data, name_col = "Name", cas_col = "CAS")
canvas_complete <- extract_meta(canvas_meta, cas = TRUE)

# Normalize and analyze
canvas_normalized <- normalize_area(canvas_complete)
canvas_filtered <- keep_area(canvas_normalized, sample_codes)

# Semi-quantification
standards <- select_std(ref_standards, 3, 14, canvas_filtered, 5)
concentrations <- calculate_con(standards, sample_weights)
results <- organize_con(concentrations, sample_codes)
```

---

## <a id="中文"></a>🧪 **labtools**: 分析化学实验室数据处理工具包

> **专为分析化学工作流程设计的综合性R包**

### 🌟 **主要功能**

- 🔍 **化学元数据提取** - 从PubChem数据库自动检索，具有智能重试机制
- 🧬 **结构数据库管理** - 构建和可视化化学结构数据库，支持高级过滤
- 📊 **质谱数据处理** - 为MS-FINDER和MS-DIAL软件导出优化数据库
- 🔬 **二维气相色谱-质谱分析** - 精确处理Canvas导出数据并合并多样本数据
- ⚗️ **化学结构转换** - SMILES到MOL文件转换，支持2D坐标生成
- 📈 **半定量工具** - 集成机器学习的高级分析定量方法
- 🎯 **交互式可视化** - 基于Shiny的化学结构导航和光谱绘图

### 🚀 **安装**

```{r installation-cn}
# 安装devtools（如果尚未安装）
if (!requireNamespace("devtools", quietly = TRUE)) {
  install.packages("devtools")
}

# 从GitHub安装labtools
devtools::install_github("QizhiSu/labtools")
```

### ⚡ **快速开始**

```{r quick-start-cn}
library(labtools)
library(dplyr)

# 🔍 1. 从PubChem提取化学元数据
data <- data.frame(
  Name = c("咖啡因", "阿司匹林", "葡萄糖"),
  CAS = c("58-08-2", "50-78-2", "50-99-7")
)

# 提取CID和综合元数据
data_with_cid <- extract_cid(data, name_col = "Name", cas_col = "CAS")
data_complete <- extract_meta(data_with_cid, cas = TRUE, flavornet = TRUE)

# 📊 2. 导出用于质谱软件
export4msdial(data_complete, polarity = "pos", output = "database_msdial.txt")
export4msfinder(data_complete, output = "database_msfinder.txt")

# ⚗️ 3. 转换SMILES为MOL文件
smiles_data <- data.frame(
  ID = c("咖啡因", "阿司匹林"),
  SMILES = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O")
)
export_smiles_to_mol(smiles_data, output_dir = "mol_files")

# 🎯 4. 交互式化学结构浏览器
navigate_chem(data_complete)  # 打开Shiny应用
```

---

## 📄 **许可证**

本项目采用MIT许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。

---

## 👨‍🔬 **作者信息**

**苏启智 (Qizhi Su)** - *包开发者*
- 📧 邮箱: <EMAIL>
- 🆔 ORCID: [0000-0002-8124-997X](https://orcid.org/0000-0002-8124-997X)
- 🐙 GitHub: [@QizhiSu](https://github.com/QizhiSu)

---

<div align="center">

**⭐ 如果您觉得labtools有用，请考虑给它一个星标！⭐**

[![GitHub stars](https://img.shields.io/github/stars/QizhiSu/labtools?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=ffd93d)](https://github.com/QizhiSu/labtools/stargazers)

---

<h3 style="color: #00d4aa;">🔬 让分析化学数据处理变得简单高效 🔬</h3>

<p style="color: #a0a0a0; font-style: italic;">
  Streamlining analytical chemistry workflows with precision and elegance
</p>

</div>