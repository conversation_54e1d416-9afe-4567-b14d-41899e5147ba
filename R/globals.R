utils::globalVariables(
  c(
    ".",
    "Area",
    "CAS",
    "Height",
    "MW",
    "Match",
    "Possibility",
    "RI",
    "RT",
    "RT_2D",
    "R_match",
    "Score",
    "Name",
    "Delta_RI",
    "NIST_RI",
    "R_RI",
    "P_RI",
    "CID",
    "InChIKey",
    "Short_InChIKey",
    "ExactMass",
    "MolecularFormula",
    "IsomericSMILES",
    "Database_ID",
    "str_remove",
    "value",
    "extract_cla",
    "SMILES",
    "Formula",
    "Adduct",
    "MZ",
    "parse.smiles",
    "par",
    "plot.new",
    "text",
    "rasterImage",
    "Average_Rt_min",
    "Average_RI",
    "Reference_RI",
    "Quant_mass",
    "Metabolite_name",
    "Total_score",
    "rowid",
    "Comment",
    "Detection_frequency",
    "Detection_rate",
    "EI_spectrum",
    "Flavornet",
    "CAS_retrieved",
    "kingdom"
  )
)
