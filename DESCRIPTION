Package: labtools
Title: Tools to Ease Handling of Data common in Analytical Chemistry Laboratories
Version: 0.3.00
Date: 2025-06-25
Authors@R:
    person("<PERSON><PERSON>", "<PERSON>", , "<EMAIL>", role = c("aut", "cre"),
           comment = c(ORCID = "https://orcid.org/0000-0002-8124-997X"))
Description: This package aims to provide a set of tools to help facilitate the
    handling of data that is commonly used in analytical chemistry laboratories,
    for example extracting metadata for chemicals from Pubchem and then build and
    view structure databases, exporting databases for MS-FINDER and MS-DIAL,
    compiling 2 dimensional GC QTOF/MS results exported from Canvas.
License: MIT + file LICENSE
Encoding: UTF-8
Roxygen: list(markdown = TRUE)
RoxygenNote: 7.3.2
Depends: R (>= 3.5.0)
Imports:
    dplyr,
    DT,
    glue,
    purrr,
    R.utils,
    rio,
    shiny,
    utils,
    webchem,
    shinyjs,
    janitor,
    tibble,
    proxy,
    randomForest,
    mspcompiler,
    classyfireR,
    rcdk
Remotes:
    QizhiSu/mspcompiler,
    aberHRML/classyfireR


