<div align="center">

# 🧪 labtools

<img src="https://img.shields.io/badge/R-276DC3?style=for-the-badge&logo=r&logoColor=white" alt="R" height="30"/>

<!-- Language Toggle -->
<div align="right" style="margin-top: -40px;">
  <a href="#english" style="color: #00d4aa; text-decoration: none; font-weight: bold;">🇺🇸 English</a> |
  <a href="#中文" style="color: #00d4aa; text-decoration: none; font-weight: bold;">🇨🇳 中文</a>
</div>

<!-- Dark Mode Badges -->
<p align="center">
  <img src="https://img.shields.io/github/workflow/status/QizhiSu/labtools/R-CMD-check?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=00d4aa" alt="R-CMD-check"/>
  <img src="https://img.shields.io/badge/License-MIT-00d4aa?style=for-the-badge&labelColor=1a1a1a" alt="License"/>
  <img src="https://img.shields.io/badge/CRAN-not%20yet-ff6b6b?style=for-the-badge&labelColor=1a1a1a" alt="CRAN"/>
  <img src="https://img.shields.io/github/stars/QizhiSu/labtools?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=ffd93d" alt="Stars"/>
  <img src="https://img.shields.io/github/issues/QizhiSu/labtools?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=ff6b6b" alt="Issues"/>
  <img src="https://img.shields.io/github/last-commit/QizhiSu/labtools?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=6c5ce7" alt="Last Commit"/>
</p>

---

<h2 style="color: #00d4aa; text-align: center;">⚗️ Analytical Chemistry Laboratory Data Processing Toolkit</h2>

<p align="center" style="font-size: 18px; color: #a0a0a0; font-style: italic;">
  A comprehensive R package for streamlining analytical chemistry workflows
</p>

</div>

---

## <a id="english"></a>🌟 **Key Features**

| Feature | Description |
|---------|-------------|
| 🔍 **Chemical Metadata Extraction** | Automated retrieval from PubChem database with intelligent retry mechanisms |
| 🧬 **Structure Database Management** | Build and visualize chemical structure databases with advanced filtering |
| 📊 **MS Data Processing** | Export optimized databases for MS-FINDER and MS-DIAL software |
| 🔬 **2D GC-MS Analysis** | Process Canvas exports and combine multi-sample data with precision |
| ⚗️ **Chemical Structure Conversion** | SMILES to MOL file conversion with 2D coordinate generation |
| 📈 **Semi-quantification Tools** | Advanced analytical quantification with machine learning integration |
| 🎯 **Interactive Visualization** | Shiny-based chemical structure navigation and spectrum plotting |

---

## 🚀 **Installation**

### Development Version (Recommended)

```r
# Install devtools if you haven't already
if (!requireNamespace("devtools", quietly = TRUE)) {
  install.packages("devtools")
}

# Install labtools from GitHub
devtools::install_github("QizhiSu/labtools")
```

### System Requirements

- **R** ≥ 4.0.0
- **Java** ≥ 8 (for rcdk package)
- **Internet connection** (for PubChem API access)

---

## ⚡ **Quick Start**

```r
library(labtools)
library(dplyr)

# 🔍 1. Extract chemical metadata from PubChem
data <- data.frame(
  Name = c("Caffeine", "Aspirin", "Glucose"),
  CAS = c("58-08-2", "50-78-2", "50-99-7")
)

# Extract CIDs and comprehensive metadata
data_with_cid <- extract_cid(data, name_col = "Name", cas_col = "CAS")
data_complete <- extract_meta(data_with_cid, cas = TRUE, flavornet = TRUE)

# 📊 2. Export for MS software
export4msdial(data_complete, polarity = "pos", output = "database_msdial.txt")
export4msfinder(data_complete, output = "database_msfinder.txt")

# ⚗️ 3. Convert SMILES to MOL files
smiles_data <- data.frame(
  ID = c("Caffeine", "Aspirin"),
  SMILES = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O")
)
export_smiles_to_mol(smiles_data, output_dir = "mol_files")

# 🎯 4. Interactive chemical structure browser
navigate_chem(data_complete)  # Opens Shiny app
```

---

## 📚 **Complete Function Reference**

### 🔍 **Chemical Metadata Functions**

<details>
<summary><strong>📋 Click to expand Chemical Metadata Functions</strong></summary>

#### `extract_cid()`
**Extract PubChem Compound IDs (CIDs) from chemical identifiers**

```r
extract_cid(data, name_col = NULL, cas_col = NULL, inchikey_col = NULL,
            timeout = 180, verbose = TRUE, checkpoint_file = "cid_checkpoint.rds",
            use_checkpoint = TRUE)
```

**Parameters:**
- `data`: Data frame with chemical identifiers
- `name_col`: Column name containing chemical names (default: `NULL`)
- `cas_col`: Column name containing CAS numbers (default: `NULL`)
- `inchikey_col`: Column name containing InChIKeys (default: `NULL`)
- `timeout`: API timeout in seconds (default: `180`)
- `verbose`: Show progress messages (default: `TRUE`)
- `checkpoint_file`: File for saving progress (default: `"cid_checkpoint.rds"`)
- `use_checkpoint`: Whether to use checkpoint functionality (default: `TRUE`)

**Usage Example:**
```r
# Basic usage with name and CAS columns
df <- data.frame(
  Name = c("Caffeine", "Aspirin"),
  CAS = c("58-08-2", "50-78-2")
)
result <- extract_cid(df, name_col = "Name", cas_col = "CAS")

# Advanced usage with all identifiers
df_advanced <- data.frame(
  Compound = c("Caffeine", "Theobromine"),
  CAS_Number = c("58-08-2", "83-67-0"),
  InChIKey = c("RYYVLZVUVIJVGH-UHFFFAOYSA-N", "YAPQBXQYLJRXSA-UHFFFAOYSA-N")
)
result <- extract_cid(df_advanced, name_col = "Compound",
                     cas_col = "CAS_Number", inchikey_col = "InChIKey",
                     timeout = 300, verbose = TRUE)
```

#### `extract_meta()`
**Extract comprehensive chemical metadata from PubChem**

```r
extract_meta(data, cas = FALSE, flavornet = FALSE, synonyms = FALSE,
             verbose = TRUE, checkpoint_dir = ".")
```

**Parameters:**
- `data`: Data frame with CID column (required)
- `cas`: Extract CAS numbers (default: `FALSE`)
- `flavornet`: Extract Flavornet sensory data (default: `FALSE`)
- `synonyms`: Extract chemical synonyms (default: `FALSE`)
- `verbose`: Show progress messages (default: `TRUE`)
- `checkpoint_dir`: Directory for checkpoint files (default: `"."`)

**Usage Example:**
```r
# Basic metadata extraction
result_basic <- extract_meta(data_with_cid)

# Comprehensive metadata extraction
result_full <- extract_meta(data_with_cid,
                           cas = TRUE,
                           flavornet = TRUE,
                           synonyms = TRUE,
                           verbose = TRUE,
                           checkpoint_dir = "./checkpoints")
```

#### `extract_classyfire()`
**Extract chemical classification data using ClassyFire**

```r
extract_classyfire(data, inchikey_col = "InChIKey", name_col = "Name")
```

**Parameters:**
- `data`: Data frame with InChIKey column
- `inchikey_col`: Column name containing InChIKeys (default: `"InChIKey"`)
- `name_col`: Column name containing compound names (default: `"Name"`)

**Usage Example:**
```r
classified_data <- extract_classyfire(data_complete,
                                     inchikey_col = "InChIKey",
                                     name_col = "Name")
```

#### `assign_meta()`
**Assign metadata from external files for compounds not in PubChem**

```r
assign_meta(data, meta_file)
```

**Parameters:**
- `data`: Data frame to update
- `meta_file`: Path to metadata file (CSV/TXT with Name, SMILES, InChIKey columns)

**Usage Example:**
```r
# Prepare metadata file (CSV with Name, SMILES, InChIKey columns)
meta_file <- "custom_compounds.csv"
updated_data <- assign_meta(data, meta_file)
```

</details>

### 📤 **Database Export Functions**

<details>
<summary><strong>📋 Click to expand Database Export Functions</strong></summary>

#### `export4msfinder()`
**Export chemical database for MS-FINDER software**

```r
export4msfinder(data, output = "database for msfinder.txt")
```

**Parameters:**
- `data`: Data frame with required columns (Name, InChIKey, CID, ExactMass, Formula, SMILES)
- `output`: Output file path (default: `"database for msfinder.txt"`)

**Usage Example:**
```r
# Basic export
export4msfinder(data_complete, output = "my_msfinder_db.txt")

# Export with custom path
export4msfinder(data_complete, output = "/path/to/databases/custom_msfinder.txt")
```

#### `export4msdial()`
**Export chemical database for MS-DIAL software with adduct calculations**

```r
export4msdial(data, polarity = "pos", output = "database for msdial.txt")
```

**Parameters:**
- `data`: Data frame with required columns (Name, ExactMass, SMILES, InChIKey)
- `polarity`: ESI polarity - `"pos"` or `"neg"` (default: `"pos"`)
- `output`: Output file path (default: `"database for msdial.txt"`)

**Usage Example:**
```r
# Positive mode export
export4msdial(data_complete, polarity = "pos", output = "pos_mode_db.txt")

# Negative mode export
export4msdial(data_complete, polarity = "neg", output = "neg_mode_db.txt")
```

#### `export_smiles_to_mol()`
**Convert SMILES strings to individual MOL files**

```r
export_smiles_to_mol(df, id_col = "ID", smiles_col = "SMILES",
                     output_dir = "mol_files")
```

**Parameters:**
- `df`: Data frame with ID and SMILES columns
- `id_col`: Column name containing compound IDs (default: `"ID"`)
- `smiles_col`: Column name containing SMILES strings (default: `"SMILES"`)
- `output_dir`: Output directory for MOL files (default: `"mol_files"`)

**Usage Example:**
```r
# Basic conversion
smiles_df <- data.frame(
  Compound_ID = c("CAFF_001", "ASP_002"),
  SMILES_String = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O")
)
result <- export_smiles_to_mol(smiles_df,
                              id_col = "Compound_ID",
                              smiles_col = "SMILES_String",
                              output_dir = "molecular_structures")
print(result)  # Shows conversion summary
```

</details>

### 📊 **Data Processing Functions**

<details>
<summary><strong>📋 Click to expand Data Processing Functions</strong></summary>

#### `read_canvas()`
**Read and combine 2D GC-MS data exported from Canvas software**

```r
read_canvas(path, ri_iden_tol = 30, ri_p_iden_tol = 100, ri_align_tol = 50,
            rt_2d_align_tol = 0.1, keep = "area")
```

**Parameters:**
- `path`: Directory path containing Canvas .txt export files
- `ri_iden_tol`: RI tolerance for identification (default: `30`)
- `ri_p_iden_tol`: Predicted RI tolerance for identification (default: `100`)
- `ri_align_tol`: RI tolerance for alignment (default: `50`)
- `rt_2d_align_tol`: 2D RT tolerance for alignment (default: `0.1`)
- `keep`: Data to keep - `"area"`, `"height"`, or `"both"` (default: `"area"`)

**Usage Example:**
```r
# Basic Canvas data processing
canvas_data <- read_canvas(
  path = "path/to/canvas/files",
  ri_align_tol = 50,
  rt_2d_align_tol = 0.1,
  keep = "area"
)

# Advanced processing with custom tolerances
canvas_data_custom <- read_canvas(
  path = "./canvas_exports",
  ri_iden_tol = 25,
  ri_p_iden_tol = 80,
  ri_align_tol = 40,
  rt_2d_align_tol = 0.05,
  keep = "both"
)
```

#### `read_msdial()`
**Read MS-DIAL output files**

```r
read_msdial(file_path, keep_unknown = FALSE, keep_spectrum = FALSE, keep_mean_sd = FALSE)
```

**Parameters:**
- `file_path`: Path to MS-DIAL output file
- `keep_unknown`: Keep unknown compounds (default: `FALSE`)
- `keep_spectrum`: Keep spectrum data (default: `FALSE`)
- `keep_mean_sd`: Keep mean and SD columns (default: `FALSE`)

**Usage Example:**
```r
# Basic MS-DIAL data reading
msdial_data <- read_msdial("msdial_output.txt")

# Advanced reading with all options
msdial_full <- read_msdial("msdial_output.txt",
                          keep_unknown = TRUE,
                          keep_spectrum = TRUE,
                          keep_mean_sd = TRUE)
```

#### `filter_msp()`
**Filter MSP spectral library files**

```r
filter_msp(msp, cmp_list, keep_napd8 = TRUE, output)
```

**Parameters:**
- `msp`: Path to the MSP library file
- `cmp_list`: Data frame with compounds to keep (must have Name and InChIKey columns)
- `keep_napd8`: Add Naphthalene-D8 to the filter list (default: `TRUE`)
- `output`: Output path for filtered MSP file

**Usage Example:**
```r
# Prepare compound list
compounds_to_keep <- data.frame(
  Name = c("Caffeine", "Aspirin"),
  InChIKey = c("RYYVLZVUVIJVGH-UHFFFAOYSA-N", "BSYNRYMUTXBXSQ-UHFFFAOYSA-N")
)

# Filter MSP library
filter_msp(msp = "NIST_library.msp",
          cmp_list = compounds_to_keep,
          keep_napd8 = TRUE,
          output = "filtered_library.msp")
```

</details>

### 🎨 **Visualization Functions**

<details>
<summary><strong>📋 Click to expand Visualization Functions</strong></summary>

#### `navigate_chem()`
**Launch interactive Shiny app for chemical structure browsing**

```r
navigate_chem(data)
```

**Parameters:**
- `data`: Data frame with chemical data (must contain SMILES column)

**Usage Example:**
```r
# Launch interactive chemical browser
navigate_chem(data_complete)  # Opens Shiny app in browser

# Works with any data frame containing SMILES
chemical_db <- data.frame(
  Name = c("Caffeine", "Aspirin"),
  SMILES = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O"),
  MW = c(194.19, 180.16)
)
navigate_chem(chemical_db)
```

#### `plot_spectrum()`
**Create interactive mass spectrum plots**

```r
plot_spectrum(spectrum, range = 10, threshold = 1, max_ticks = 20)
```

**Parameters:**
- `spectrum`: Named numeric vector (m/z as names, intensity as values)
- `range`: Bin width for peak labeling (default: `10`)
- `threshold`: Minimum intensity (%) for peak labeling (default: `1`)
- `max_ticks`: Maximum number of x-axis ticks (default: `20`)

**Usage Example:**
```r
# Create spectrum from string
spectrum_str <- "100:30 120:80 145:60 170:90 200:20"
spectrum <- update_spectrum(spectrum_str)
plot_spectrum(spectrum)

# Advanced plotting with custom parameters
plot_spectrum(spectrum, range = 5, threshold = 5, max_ticks = 15)
```

</details>

### 🛠️ **Utility Functions**

<details>
<summary><strong>📋 Click to expand Utility Functions</strong></summary>

#### **Spectral Analysis Functions**

##### `update_spectrum()`
**Convert spectral strings to numeric vectors**

```r
update_spectrum(spectrum_str, start_mz = 50, end_mz = 500, mz_step = 1, digits = 0)
```

**Parameters:**
- `spectrum_str`: Spectrum string in "mz:intensity mz:intensity" format
- `start_mz`: Starting m/z value (default: `50`)
- `end_mz`: Ending m/z value (default: `500`)
- `mz_step`: m/z step size (default: `1`)
- `digits`: Decimal places for m/z rounding (default: `0`)

**Usage Example:**
```r
# Convert spectrum string to numeric vector
spectrum_str <- "98.7:10 99.9:20 101.2:15"
spectrum <- update_spectrum(spectrum_str, start_mz = 98, end_mz = 102)
```

##### `cosine_similarity()`
**Calculate spectral similarity**

```r
cosine_similarity(spectrum1, spectrum2)
```

**Usage Example:**
```r
# Calculate similarity between two spectra
spec1 <- update_spectrum("100:30 120:80 145:60")
spec2 <- update_spectrum("100:25 120:85 145:55")
similarity <- cosine_similarity(spec1, spec2)
```

##### `msp2df()` / `df2msp()`
**Convert between MSP and data frame formats**

```r
msp2df(msp_data)
df2msp(df_data, output_file)
```

#### **Semi-quantification Functions**

##### `select_std()`
**Select semi-quantification standards using molecular similarity**

```r
select_std(std_md, std_res_col, std_md1_col, data_md, data_md1_col, top_npct_md = 20)
```

**Parameters:**
- `std_md`: Standards data frame with response and molecular descriptors
- `std_res_col`: Column index of response variable (or `FALSE` to use all descriptors)
- `std_md1_col`: Starting column index of molecular descriptors in standards
- `data_md`: Target compounds data frame with molecular descriptors
- `data_md1_col`: Starting column index of molecular descriptors in targets
- `top_npct_md`: Percentage of top molecular descriptors to use (default: `20`)

**Usage Example:**
```r
# Select standards using molecular similarity
standards <- select_std(std_md = reference_standards,
                       std_res_col = 3,
                       std_md1_col = 14,
                       data_md = target_compounds,
                       data_md1_col = 5,
                       top_npct_md = 30)

# Use all molecular descriptors (no feature selection)
standards_all <- select_std(std_md = reference_standards,
                           std_res_col = FALSE,
                           std_md1_col = 14,
                           data_md = target_compounds,
                           data_md1_col = 5)
```

##### `calculate_con()`
**Calculate concentrations using semi-quantification**

```r
calculate_con(df, sam_weight, start_col = 12)
```

**Parameters:**
- `df`: Data frame with peak areas and calibration data
- `sam_weight`: Data frame with sample weights and volumes (can be `NULL`)
- `start_col`: Starting column index of peak area data (default: `12`)

**Usage Example:**
```r
# Calculate concentrations with sample weights
sample_weights <- data.frame(
  Name = c("Sample1", "Sample2"),
  Weight = c(0.5, 0.6),
  Volumn = c(10, 10)
)
concentrations <- calculate_con(df_with_areas, sam_weight = sample_weights)

# Calculate without sample weights (instrument concentrations only)
concentrations_inst <- calculate_con(df_with_areas, sam_weight = NULL)
```

##### `organize_con()`
**Organize and summarize concentration data**

```r
organize_con(df, sam_code, start_col = 12, digits = 2, na2zero = TRUE, bind_mean_sd = TRUE)
```

**Parameters:**
- `df`: Data frame after `calculate_con()` processing
- `sam_code`: Data frame with sample codes and names
- `start_col`: Starting column index of concentration data (default: `12`)
- `digits`: Decimal places for rounding (default: `2`)
- `na2zero`: Replace NA with zero for calculations (default: `TRUE`)
- `bind_mean_sd`: Combine mean and SD in single columns (default: `TRUE`)

**Usage Example:**
```r
# Organize concentration data
sample_codes <- data.frame(
  Code = c("S1", "S2", "S3"),
  Name = c("Sample1", "Sample2", "Sample3")
)
organized_data <- organize_con(concentrations,
                              sam_code = sample_codes,
                              digits = 3,
                              bind_mean_sd = FALSE)
```

#### **Data Processing Functions**

##### `keep_area()`
**Filter peak areas based on sample codes**

```r
keep_area(df, sam_code, start_col = 12, keep_bk = TRUE, keep_d8 = TRUE)
```

**Parameters:**
- `df`: Data frame with peak area data
- `sam_code`: Data frame mapping sample codes to names
- `start_col`: Starting column index of area data (default: `12`)
- `keep_bk`: Keep blank samples (default: `TRUE`)
- `keep_d8`: Keep D8 internal standard (default: `TRUE`)

##### `normalize_area()`
**Normalize peak areas by D8 internal standard**

```r
normalize_area(df, start_col = 12)
```

**Parameters:**
- `df`: Data frame with peak area data
- `start_col`: Starting column index of area data (default: `12`)

**Usage Example:**
```r
# Normalize areas by D8 internal standard
normalized_data <- normalize_area(raw_data, start_col = 12)

# Filter areas based on sample codes
filtered_data <- keep_area(normalized_data,
                          sam_code = sample_mapping,
                          keep_bk = FALSE)
```

</details>

---

## 🔬 **Advanced Workflows**

### Workflow 1: Complete Chemical Database Creation

```r
# Step 1: Prepare your data
chemicals <- data.frame(
  Name = c("Benzene", "Toluene", "Xylene"),
  CAS = c("71-43-2", "108-88-3", "1330-20-7")
)

# Step 2: Extract comprehensive metadata
chemicals_cid <- extract_cid(chemicals, name_col = "Name", cas_col = "CAS")
chemicals_meta <- extract_meta(chemicals_cid, cas = TRUE, synonyms = TRUE, flavornet = TRUE)

# Step 3: Add chemical classification
chemicals_class <- extract_classyfire(chemicals_meta)

# Step 4: Export for different MS software
export4msdial(chemicals_class, polarity = "pos", output = "pos_database.txt")
export4msdial(chemicals_class, polarity = "neg", output = "neg_database.txt")
export4msfinder(chemicals_class, output = "msfinder_database.txt")

# Step 5: Generate MOL files for structure visualization
export_smiles_to_mol(chemicals_class, output_dir = "structures")
```

### Workflow 2: 2D GC-MS Data Processing Pipeline

```r
# Step 1: Process Canvas exports
canvas_data <- read_canvas(
  path = "path/to/canvas/files",
  ri_align_tol = 50,
  rt_2d_align_tol = 0.1,
  keep = "area"
)

# Step 2: Extract metadata for identified compounds
canvas_meta <- extract_cid(canvas_data, name_col = "Name", cas_col = "CAS")
canvas_complete <- extract_meta(canvas_meta, cas = TRUE, flavornet = TRUE)

# Step 3: Normalize and filter data
canvas_normalized <- normalize_area(canvas_complete)
canvas_filtered <- keep_area(canvas_normalized, sam_code = sample_codes)

# Step 4: Semi-quantification analysis
standards_assigned <- select_std(reference_standards, 3, 14, canvas_filtered, 5)
concentrations <- calculate_con(standards_assigned, sample_weights)
final_results <- organize_con(concentrations, sample_codes)
```

### Workflow 3: Spectral Library Processing

```r
# Step 1: Read MS-DIAL results
msdial_data <- read_msdial("msdial_results.txt", keep_spectrum = TRUE)

# Step 2: Extract metadata for compounds
compounds_meta <- extract_cid(msdial_data, name_col = "Name")
compounds_complete <- extract_meta(compounds_meta, cas = TRUE)

# Step 3: Filter spectral library
filter_msp(msp = "NIST_library.msp",
          cmp_list = compounds_complete,
          output = "targeted_library.msp")

# Step 4: Analyze spectra
for(i in 1:nrow(compounds_complete)) {
  if(!is.na(compounds_complete$Spectrum[i])) {
    spectrum <- update_spectrum(compounds_complete$Spectrum[i])
    plot_spectrum(spectrum)
  }
}
```

---

## 📖 **Detailed Examples**

<details>
<summary><strong>🔍 Complete Metadata Extraction Example</strong></summary>

```r
library(labtools)
library(dplyr)

# Create comprehensive sample data
compounds <- data.frame(
  Compound_Name = c("Caffeine", "Theobromine", "Theophylline", "Vanillin"),
  CAS_Number = c("58-08-2", "83-67-0", "58-55-9", "121-33-5"),
  InChIKey = c("RYYVLZVUVIJVGH-UHFFFAOYSA-N",
               "YAPQBXQYLJRXSA-UHFFFAOYSA-N",
               "ZFXYFBGIUFBOJW-UHFFFAOYSA-N",
               "MWOOGOJBHIARFG-UHFFFAOYSA-N"),
  stringsAsFactors = FALSE
)

# Extract CIDs with comprehensive error handling
compounds_cid <- extract_cid(
  compounds,
  name_col = "Compound_Name",
  cas_col = "CAS_Number",
  inchikey_col = "InChIKey",
  timeout = 300,
  verbose = TRUE,
  checkpoint_file = "comprehensive_cid_checkpoint.rds"
)

# Extract all available metadata
compounds_complete <- extract_meta(
  compounds_cid,
  cas = TRUE,
  flavornet = TRUE,
  synonyms = TRUE,
  verbose = TRUE,
  checkpoint_dir = "./metadata_checkpoints"
)

# Add chemical classification
compounds_classified <- extract_classyfire(compounds_complete)

# View comprehensive results
glimpse(compounds_classified)
print(paste("Successfully processed", nrow(compounds_classified), "compounds"))
```

</details>

<details>
<summary><strong>🧬 Advanced SMILES Processing Example</strong></summary>

```r
# Prepare diverse SMILES data
smiles_data <- data.frame(
  Compound_ID = c("CAFF_001", "THEO_002", "THEOP_003", "VAN_004"),
  Compound_Name = c("Caffeine", "Theobromine", "Theophylline", "Vanillin"),
  SMILES_String = c(
    "Cn1cnc2c1c(=O)n(c(=O)n2C)C",           # Caffeine
    "Cn1cnc2c1c(=O)[nH]c(=O)n2C",           # Theobromine
    "Cn1c(=O)c2[nH]cnc2n(C)c1=O",           # Theophylline
    "COc1cc(C=O)ccc1O"                      # Vanillin
  ),
  Category = c("Alkaloid", "Alkaloid", "Alkaloid", "Aldehyde"),
  stringsAsFactors = FALSE
)

# Convert to MOL files with detailed tracking
result <- export_smiles_to_mol(
  smiles_data,
  id_col = "Compound_ID",
  smiles_col = "SMILES_String",
  output_dir = "molecular_structures_detailed"
)

# Analyze conversion results
cat("Conversion Summary:\n")
cat("Successful conversions:", result$success, "\n")
cat("Failed conversions:", result$failed, "\n")
cat("Skipped entries:", result$skipped, "\n")

# Verify MOL files were created
mol_files <- list.files("molecular_structures_detailed", pattern = "\\.mol$")
cat("MOL files created:", length(mol_files), "\n")
cat("Files:", paste(mol_files, collapse = ", "), "\n")
```

</details>

<details>
<summary><strong>📊 Semi-quantification Workflow Example</strong></summary>

```r
# Prepare reference standards data
reference_standards <- data.frame(
  Name = c("Standard_A", "Standard_B", "Standard_C"),
  Slope = c(1.2, 0.8, 1.5),
  Intercept = c(0.1, 0.05, 0.2),
  R2 = c(0.995, 0.998, 0.992),
  Low_con = c(0.1, 0.05, 0.2),
  Low_area = c(1000, 800, 1200),
  # Molecular descriptors start from column 7
  MD1 = c(2.3, 1.8, 2.1),
  MD2 = c(0.5, 0.7, 0.6),
  MD3 = c(1.2, 1.0, 1.4)
)

# Prepare target compounds data
target_compounds <- data.frame(
  Name = c("Compound_X", "Compound_Y", "Compound_Z"),
  CAS = c("123-45-6", "789-01-2", "345-67-8"),
  # Molecular descriptors start from column 3
  MD1 = c(2.2, 1.9, 2.0),
  MD2 = c(0.6, 0.8, 0.5),
  MD3 = c(1.1, 1.2, 1.3)
)

# Select appropriate standards using molecular similarity
standards_assigned <- select_std(
  std_md = reference_standards,
  std_res_col = 2,  # Slope column
  std_md1_col = 7,  # First molecular descriptor column
  data_md = target_compounds,
  data_md1_col = 3, # First molecular descriptor column
  top_npct_md = 50  # Use top 50% of descriptors
)

# View standard assignments
print("Standard assignments:")
print(standards_assigned[, c("Name", "Standard", "Similarity")])

# Prepare sample weight data
sample_weights <- data.frame(
  Name = c("Sample1", "Sample2", "Sample3"),
  Weight = c(0.5, 0.6, 0.55),  # grams
  Volumn = c(10, 10, 10)       # mL
)

# Add mock peak area data for demonstration
area_data <- matrix(runif(9, 1000, 5000), nrow = 3)
colnames(area_data) <- paste0("X1_", sample_weights$Name)
standards_with_areas <- cbind(standards_assigned, area_data)

# Calculate concentrations
concentrations <- calculate_con(
  df = standards_with_areas,
  sam_weight = sample_weights,
  start_col = ncol(standards_assigned) + 1
)

# Organize results
sample_codes <- data.frame(
  Code = c("S1", "S2", "S3"),
  Name = sample_weights$Name
)

final_results <- organize_con(
  df = concentrations,
  sam_code = sample_codes,
  start_col = ncol(standards_assigned) + 1,
  digits = 3,
  bind_mean_sd = TRUE
)

print("Final concentration results:")
print(final_results[, c("Name", "Standard", "Minimum", "Median", "Maximum", "Mean")])
```

</details>

---

## <a id="中文"></a>🧪 **labtools**: 分析化学实验室数据处理工具包

> **专为分析化学工作流程设计的综合性R包**

### 🌟 **主要功能**

| 功能 | 描述 |
|------|------|
| 🔍 **化学元数据提取** | 从PubChem数据库自动检索，具有智能重试机制 |
| 🧬 **结构数据库管理** | 构建和可视化化学结构数据库，支持高级过滤 |
| 📊 **质谱数据处理** | 为MS-FINDER和MS-DIAL软件导出优化数据库 |
| 🔬 **二维气相色谱-质谱分析** | 精确处理Canvas导出数据并合并多样本数据 |
| ⚗️ **化学结构转换** | SMILES到MOL文件转换，支持2D坐标生成 |
| 📈 **半定量工具** | 集成机器学习的高级分析定量方法 |
| 🎯 **交互式可视化** | 基于Shiny的化学结构导航和光谱绘图 |

### 🚀 **安装**

```r
# 安装devtools（如果尚未安装）
if (!requireNamespace("devtools", quietly = TRUE)) {
  install.packages("devtools")
}

# 从GitHub安装labtools
devtools::install_github("QizhiSu/labtools")
```

### ⚡ **快速开始**

```r
library(labtools)
library(dplyr)

# 🔍 1. 从PubChem提取化学元数据
data <- data.frame(
  Name = c("咖啡因", "阿司匹林", "葡萄糖"),
  CAS = c("58-08-2", "50-78-2", "50-99-7")
)

# 提取CID和综合元数据
data_with_cid <- extract_cid(data, name_col = "Name", cas_col = "CAS")
data_complete <- extract_meta(data_with_cid, cas = TRUE, flavornet = TRUE)

# 📊 2. 导出用于质谱软件
export4msdial(data_complete, polarity = "pos", output = "database_msdial.txt")
export4msfinder(data_complete, output = "database_msfinder.txt")

# ⚗️ 3. 转换SMILES为MOL文件
smiles_data <- data.frame(
  ID = c("咖啡因", "阿司匹林"),
  SMILES = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O")
)
export_smiles_to_mol(smiles_data, output_dir = "mol_files")

# 🎯 4. 交互式化学结构浏览器
navigate_chem(data_complete)  # 打开Shiny应用
```

---

## 🤝 **贡献指南**

我们欢迎各种形式的贡献！

### 开发环境设置

```r
# 克隆仓库
git clone https://github.com/QizhiSu/labtools.git

# 安装开发依赖
devtools::install_dev_deps()

# 运行测试
devtools::test()

# 检查包
devtools::check()
```

### 报告问题

请在我们的 [GitHub Issues](https://github.com/QizhiSu/labtools/issues) 页面报告错误和功能请求。

---

## 📄 **许可证**

本项目采用MIT许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。

---

## 👨‍🔬 **作者信息**

**苏启智 (Qizhi Su)** - *包开发者*
- 📧 邮箱: <EMAIL>
- 🆔 ORCID: [0000-0002-8124-997X](https://orcid.org/0000-0002-8124-997X)
- 🐙 GitHub: [@QizhiSu](https://github.com/QizhiSu)

---

## 📚 **引用**

如果您在研究中使用labtools，请引用：

```bibtex
@Manual{labtools,
  title = {labtools: Tools to Ease Handling of Data common in Analytical Chemistry Laboratories},
  author = {Qizhi Su},
  year = {2025},
  note = {R package version 0.3.00},
  url = {https://github.com/QizhiSu/labtools}
}
```

---

## 🔄 **版本历史**

### 版本 0.3.00 (2025-06-25)
- **新增:** 添加了`export_smiles_to_mol()`函数，用于将SMILES字符串转换为MOL文件
- **改进:** 增强了错误处理和输入验证，修复了所有非ASCII字符问题
- **修复:** 各种错误修复和代码质量改进，完善了函数文档

### 之前版本
- **0.2.01** (2024-09-07): 修复了`extract_cid()`中的错误，并为PubChem API添加了超时选项
- **0.2.00** (2024-07-06): 添加了半定量分析功能
- **0.1.01** (2024-04-21): 添加了过滤MSP文件的功能
- **0.0.7.0000** (2024-03-12): 添加了分配半定量标准的功能
- **0.0.6.0000** (2023-11-28): 添加了处理MS-DIAL GCMS数据的功能

---

<div align="center">

**⭐ 如果您觉得labtools有用，请考虑给它一个星标！⭐**

[![GitHub stars](https://img.shields.io/github/stars/QizhiSu/labtools?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=ffd93d)](https://github.com/QizhiSu/labtools/stargazers)

---

<h3 style="color: #00d4aa;">🔬 让分析化学数据处理变得简单高效 🔬</h3>

<p style="color: #a0a0a0; font-style: italic;">
  Streamlining analytical chemistry workflows with precision and elegance
</p>

</div>