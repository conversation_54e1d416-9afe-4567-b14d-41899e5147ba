# labtools <img src="https://img.shields.io/badge/R-276DC3?style=for-the-badge&logo=r&logoColor=white" alt="R" height="20"/>

<!-- Language Toggle -->
<div align="right">
  <a href="#english">🇺🇸 English</a> | <a href="#中文">🇨🇳 中文</a>
</div>

<!-- Badges -->
<div align="center">

[![R-CMD-check](https://github.com/QizhiSu/labtools/workflows/R-CMD-check/badge.svg)](https://github.com/QizhiSu/labtools/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![CRAN status](https://www.r-pkg.org/badges/version/labtools)](https://CRAN.R-project.org/package=labtools)
[![Downloads](https://cranlogs.r-pkg.org/badges/grand-total/labtools)](https://cran.r-project.org/package=labtools)
[![GitHub issues](https://img.shields.io/github/issues/QizhiSu/labtools)](https://github.com/QizhiSu/labtools/issues)
[![GitHub stars](https://img.shields.io/github/stars/QizhiSu/labtools)](https://github.com/QizhiSu/labtools/stargazers)

</div>

---

## <a id="english"></a>🧪 **labtools**: Analytical Chemistry Laboratory Data Processing Toolkit

> **A comprehensive R package for streamlining analytical chemistry workflows**

**labtools** provides a powerful suite of tools designed specifically for analytical chemistry laboratories. From chemical metadata extraction to mass spectrometry data processing, this package simplifies complex workflows and enhances research productivity.

### 🌟 **Key Features**

- 🔍 **Chemical Metadata Extraction** - Automated retrieval from PubChem database
- 🧬 **Structure Database Management** - Build and visualize chemical structure databases
- 📊 **MS Data Processing** - Export databases for MS-FINDER and MS-DIAL
- 🔬 **2D GC-MS Analysis** - Process Canvas exports and combine multi-sample data
- ⚗️ **Chemical Structure Conversion** - SMILES to MOL file conversion
- 📈 **Semi-quantification Tools** - Advanced analytical quantification methods
- 🎯 **Interactive Visualization** - Shiny-based chemical structure navigation

### 📋 **Table of Contents**

- [Installation](#installation)
- [Quick Start](#quick-start)
- [Function Reference](#function-reference)
  - [Chemical Metadata Functions](#chemical-metadata-functions)
  - [Database Export Functions](#database-export-functions)
  - [Data Processing Functions](#data-processing-functions)
  - [Visualization Functions](#visualization-functions)
  - [Utility Functions](#utility-functions)
- [Workflows](#workflows)
- [Examples](#examples)
- [Contributing](#contributing)
- [License](#license)

---

## 🚀 **Installation** <a id="installation"></a>

### Development Version (Recommended)

```r
# Install devtools if you haven't already
if (!requireNamespace("devtools", quietly = TRUE)) {
  install.packages("devtools")
}

# Install labtools from GitHub
devtools::install_github("QizhiSu/labtools")
```

### Dependencies

The package automatically installs required dependencies:

| Category | Packages |
|----------|----------|
| **Data Manipulation** | `dplyr`, `tibble`, `purrr`, `janitor` |
| **Chemical Informatics** | `rcdk`, `webchem`, `classyfireR` |
| **File I/O** | `rio`, `R.utils` |
| **Visualization** | `shiny`, `DT`, `shinyjs`, `ggplot2`, `plotly` |
| **Analysis** | `randomForest`, `proxy` |

---

## ⚡ **Quick Start** <a id="quick-start"></a>

```r
library(labtools)
library(dplyr)

# 1. Extract chemical metadata from PubChem
data <- data.frame(
  Name = c("Caffeine", "Aspirin", "Glucose"),
  CAS = c("58-08-2", "50-78-2", "50-99-7")
)

# Extract CIDs and metadata
data_with_cid <- extract_cid(data, name_col = "Name", cas_col = "CAS")
data_complete <- extract_meta(data_with_cid, cas = TRUE, flavornet = TRUE)

# 2. Export for MS software
export4msdial(data_complete, polarity = "pos", output = "database_msdial.txt")
export4msfinder(data_complete, output = "database_msfinder.txt")

# 3. Convert SMILES to MOL files
smiles_data <- data.frame(
  ID = c("Caffeine", "Aspirin"),
  SMILES = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O")
)
export_smiles_to_mol(smiles_data, output_dir = "mol_files")

# 4. Interactive chemical structure browser
navigate_chem(data_complete)  # Opens Shiny app
```

---

## 📚 **Function Reference** <a id="function-reference"></a>

### 🔍 **Chemical Metadata Functions** <a id="chemical-metadata-functions"></a>

#### `extract_cid()`
Extract PubChem Compound IDs (CIDs) from chemical identifiers.

```r
extract_cid(data, name_col = NULL, cas_col = NULL, inchikey_col = NULL,
            timeout = 180, verbose = TRUE, checkpoint_file = "cid_checkpoint.rds")
```

**Parameters:**
- `data`: Data frame with chemical identifiers
- `name_col`: Column name containing chemical names
- `cas_col`: Column name containing CAS numbers
- `inchikey_col`: Column name containing InChIKeys
- `timeout`: API timeout in seconds
- `verbose`: Show progress messages
- `checkpoint_file`: File for saving progress

#### `extract_meta()`
Extract comprehensive chemical metadata from PubChem.

```r
extract_meta(data, cas = FALSE, flavornet = FALSE, synonyms = FALSE,
             verbose = TRUE, checkpoint_dir = ".")
```

**Parameters:**
- `data`: Data frame with CID column
- `cas`: Extract CAS numbers
- `flavornet`: Extract Flavornet sensory data
- `synonyms`: Extract chemical synonyms
- `verbose`: Show progress messages
- `checkpoint_dir`: Directory for checkpoint files

#### `extract_classyfire()`
Extract chemical classification data using ClassyFire.

```r
extract_classyfire(data, inchikey_col = "InChIKey", name_col = "Name")
```

#### `assign_meta()`
Assign metadata from external files for compounds not in PubChem.

```r
assign_meta(data, meta_file)
```

### 📤 **Database Export Functions** <a id="database-export-functions"></a>

#### `export4msfinder()`
Export chemical database for MS-FINDER software.

```r
export4msfinder(data, output = "database_for_msfinder.txt")
```

#### `export4msdial()`
Export chemical database for MS-DIAL software with adduct calculations.

```r
export4msdial(data, polarity = "pos", output = "database_for_msdial.txt")
```

#### `export_smiles_to_mol()`
Convert SMILES strings to individual MOL files.

```r
export_smiles_to_mol(df, id_col = "ID", smiles_col = "SMILES",
                     output_dir = "mol_files")
```

### 📊 **Data Processing Functions** <a id="data-processing-functions"></a>

#### `read_canvas()`
Read and combine 2D GC-MS data exported from Canvas software.

```r
read_canvas(data_path, ri_align_tolerance = 5, rt_2d_tolerance = 0.05,
            keep = "area")
```

#### `read_msdial()`
Read MS-DIAL output files.

```r
read_msdial(file_path)
```

#### `filter_msp()`
Filter MSP spectral library files.

```r
filter_msp(msp, keep_napd8 = TRUE, cmp_list = NULL, output = "filtered.msp")
```

### 🎨 **Visualization Functions** <a id="visualization-functions"></a>

#### `navigate_chem()`
Launch interactive Shiny app for chemical structure browsing.

```r
navigate_chem(data)
```

#### `plot_spectrum()`
Create interactive mass spectrum plots.

```r
plot_spectrum(spectrum, range = 10, threshold = 1, max_ticks = 20)
```

### 🛠️ **Utility Functions** <a id="utility-functions"></a>

#### Spectral Analysis
- `update_spectrum()`: Convert spectral strings to numeric vectors
- `cosine_similarity()`: Calculate spectral similarity
- `msp2df()` / `df2msp()`: Convert between MSP and data frame formats

#### Semi-quantification
- `select_std()`: Select semi-quantification standards
- `calculate_con()`: Calculate concentrations
- `calculate_freq()`: Calculate detection frequencies
- `organize_con()`: Organize concentration data

#### Data Processing
- `keep_area()`: Filter by peak area
- `normalize_area()`: Normalize peak areas

---

## 🔬 **Workflows** <a id="workflows"></a>

### Workflow 1: Chemical Database Creation

```r
# Step 1: Prepare your data
chemicals <- data.frame(
  Name = c("Benzene", "Toluene", "Xylene"),
  CAS = c("71-43-2", "108-88-3", "1330-20-7")
)

# Step 2: Extract metadata
chemicals_cid <- extract_cid(chemicals, name_col = "Name", cas_col = "CAS")
chemicals_meta <- extract_meta(chemicals_cid, cas = TRUE, synonyms = TRUE)

# Step 3: Add classification
chemicals_class <- extract_classyfire(chemicals_meta)

# Step 4: Export for MS software
export4msdial(chemicals_class, polarity = "pos", output = "my_database.txt")
```

### Workflow 2: 2D GC-MS Data Processing

```r
# Process Canvas exports
canvas_data <- read_canvas(
  data_path = "path/to/canvas/files",
  ri_align_tolerance = 5,
  rt_2d_tolerance = 0.05,
  keep = "area"
)

# Normalize and filter
canvas_normalized <- normalize_area(canvas_data)
canvas_filtered <- keep_area(canvas_normalized, min_area = 1000)
```

---

## 📖 **Examples** <a id="examples"></a>

<details>
<summary><strong>🔍 Complete Metadata Extraction Example</strong></summary>

```r
library(labtools)
library(dplyr)

# Create sample data
compounds <- data.frame(
  Compound_Name = c("Caffeine", "Theobromine", "Theophylline"),
  CAS_Number = c("58-08-2", "83-67-0", "58-55-9"),
  stringsAsFactors = FALSE
)

# Extract CIDs
compounds_cid <- extract_cid(
  compounds,
  name_col = "Compound_Name",
  cas_col = "CAS_Number",
  verbose = TRUE
)

# Extract comprehensive metadata
compounds_complete <- extract_meta(
  compounds_cid,
  cas = TRUE,
  flavornet = TRUE,
  synonyms = TRUE,
  verbose = TRUE
)

# Add chemical classification
compounds_classified <- extract_classyfire(compounds_complete)

# View results
glimpse(compounds_classified)
```

</details>

<details>
<summary><strong>🧬 SMILES to MOL Conversion Example</strong></summary>

```r
# Prepare SMILES data
smiles_data <- data.frame(
  Compound_ID = c("CAFF_001", "THEO_002", "THEOP_003"),
  SMILES_String = c(
    "Cn1cnc2c1c(=O)n(c(=O)n2C)C",      # Caffeine
    "Cn1cnc2c1c(=O)[nH]c(=O)n2C",      # Theobromine
    "Cn1c(=O)c2[nH]cnc2n(C)c1=O"       # Theophylline
  ),
  stringsAsFactors = FALSE
)

# Convert to MOL files
result <- export_smiles_to_mol(
  smiles_data,
  id_col = "Compound_ID",
  smiles_col = "SMILES_String",
  output_dir = "molecular_structures"
)

# Check conversion results
print(result)
# $success: 3, $failed: 0, $skipped: 0
```

</details>

---

## <a id="中文"></a>🧪 **labtools**: 分析化学实验室数据处理工具包

> **专为分析化学工作流程设计的综合性R包**

**labtools** 为分析化学实验室提供了一套强大的工具。从化学元数据提取到质谱数据处理，该包简化了复杂的工作流程，提高了研究效率。

### 🌟 **主要功能**

- 🔍 **化学元数据提取** - 从PubChem数据库自动检索
- 🧬 **结构数据库管理** - 构建和可视化化学结构数据库
- 📊 **质谱数据处理** - 导出MS-FINDER和MS-DIAL数据库
- 🔬 **二维气相色谱-质谱分析** - 处理Canvas导出数据并合并多样本数据
- ⚗️ **化学结构转换** - SMILES到MOL文件转换
- 📈 **半定量工具** - 高级分析定量方法
- 🎯 **交互式可视化** - 基于Shiny的化学结构浏览

### 📋 **目录**

- [安装](#安装)
- [快速开始](#快速开始)
- [函数参考](#函数参考)
- [工作流程](#工作流程)
- [示例](#示例)
- [贡献](#贡献)
- [许可证](#许可证)

---

## 🚀 **安装** <a id="安装"></a>

### 开发版本（推荐）

```r
# 如果尚未安装devtools，请先安装
if (!requireNamespace("devtools", quietly = TRUE)) {
  install.packages("devtools")
}

# 从GitHub安装labtools
devtools::install_github("QizhiSu/labtools")
```

---

## ⚡ **快速开始** <a id="快速开始"></a>

```r
library(labtools)
library(dplyr)

# 1. 从PubChem提取化学元数据
data <- data.frame(
  Name = c("咖啡因", "阿司匹林", "葡萄糖"),
  CAS = c("58-08-2", "50-78-2", "50-99-7")
)

# 提取CID和元数据
data_with_cid <- extract_cid(data, name_col = "Name", cas_col = "CAS")
data_complete <- extract_meta(data_with_cid, cas = TRUE, flavornet = TRUE)

# 2. 导出用于质谱软件
export4msdial(data_complete, polarity = "pos", output = "database_msdial.txt")
export4msfinder(data_complete, output = "database_msfinder.txt")

# 3. 转换SMILES为MOL文件
smiles_data <- data.frame(
  ID = c("咖啡因", "阿司匹林"),
  SMILES = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O")
)
export_smiles_to_mol(smiles_data, output_dir = "mol_files")

# 4. 交互式化学结构浏览器
navigate_chem(data_complete)  # 打开Shiny应用
```

---

## 🤝 **贡献** <a id="contributing"></a>

我们欢迎贡献！请查看我们的[贡献指南](CONTRIBUTING.md)了解详情。

### 开发环境设置

```r
# 克隆仓库
git clone https://github.com/QizhiSu/labtools.git

# 安装开发依赖
devtools::install_dev_deps()

# 运行测试
devtools::test()

# 检查包
devtools::check()
```

### 报告问题

请在我们的[GitHub Issues](https://github.com/QizhiSu/labtools/issues)页面报告错误和功能请求。

---

## 📄 **许可证** <a id="license"></a>

本项目采用MIT许可证 - 详情请参阅[LICENSE](LICENSE)文件。

---

## 👨‍🔬 **作者**

**苏启智** - *包开发者*
- 📧 邮箱: <EMAIL>
- 🆔 ORCID: [0000-0002-8124-997X](https://orcid.org/0000-0002-8124-997X)
- 🐙 GitHub: [@QizhiSu](https://github.com/QizhiSu)

---

## 📚 **引用**

如果您在研究中使用labtools，请引用：

```bibtex
@Manual{labtools,
  title = {labtools: Tools to Ease Handling of Data common in Analytical Chemistry Laboratories},
  author = {Qizhi Su},
  year = {2025},
  note = {R package version 0.3.00},
  url = {https://github.com/QizhiSu/labtools}
}
```

---

## 🔄 **版本历史**

### 版本 0.3.00 (2025-06-25)
- **新增:** 添加了`export_smiles_to_mol()`函数，用于将SMILES字符串转换为MOL文件
- **改进:** 增强了错误处理和输入验证
- **修复:** 各种错误修复和代码质量改进

### 之前版本
- **0.2.01** (2024-09-07): 修复了`extract_cid()`中的错误，并为PubChem API添加了超时选项
- **0.2.00** (2024-07-06): 添加了半定量分析功能
- **0.1.01** (2024-04-21): 添加了过滤MSP文件的功能
- **0.0.7.0000** (2024-03-12): 添加了分配半定量标准的功能
- **0.0.6.0000** (2023-11-28): 添加了处理MS-DIAL GCMS数据的功能

---

<div align="center">

**⭐ 如果您觉得labtools有用，请考虑给它一个星标！⭐**

[![GitHub stars](https://img.shields.io/github/stars/QizhiSu/labtools?style=social)](https://github.com/QizhiSu/labtools/stargazers)

---

**🔬 让分析化学数据处理变得简单高效 🔬**

</div>
