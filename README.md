
<!-- README.md is generated from README.Rmd. Please edit that file -->
<!-- Modern Dark Theme CSS -->
<style>
body {
  background-color: #0d1117;
  color: #e6edf3;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
  line-height: 1.6;
}
&#10;.main-header {
  background: linear-gradient(135deg, #1c2128 0%, #21262d 100%);
  border-radius: 16px;
  padding: 40px 30px;
  margin: 30px auto;
  max-width: 900px;
  border: 1px solid #30363d;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  position: relative;
}
&#10;.language-toggle {
  position: absolute;
  top: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}
&#10;.language-toggle a {
  color: #58a6ff;
  text-decoration: none;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}
&#10;.language-toggle a:hover {
  background-color: rgba(88, 166, 255, 0.1);
}
&#10;.title-section {
  text-align: center;
  margin-bottom: 25px;
}
&#10;.main-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #58a6ff;
  margin: 0 0 15px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
&#10;.r-badge {
  margin: 15px 0;
}
&#10;.subtitle {
  font-size: 1.4rem;
  color: #7c3aed;
  font-weight: 600;
  margin: 20px 0 15px 0;
  text-align: center;
}
&#10;.description {
  font-size: 1.1rem;
  color: #8b949e;
  font-style: italic;
  text-align: center;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}
&#10;.badges-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
  margin: 25px 0 0 0;
}
&#10;.feature-table {
  background: #161b22;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #30363d;
  margin: 20px 0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}
&#10;.feature-table table {
  width: 100%;
  border-collapse: collapse;
}
&#10;.feature-table th, .feature-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #21262d;
  text-align: left;
}
&#10;.feature-table th {
  background: #21262d;
  color: #58a6ff;
  font-weight: 600;
  font-size: 1.1rem;
}
&#10;.feature-table td {
  color: #e6edf3;
}
&#10;.feature-table tr:last-child td {
  border-bottom: none;
}
&#10;.code-block {
  background: #161b22;
  border-radius: 8px;
  border: 1px solid #30363d;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
&#10;.info-box {
  background: linear-gradient(135deg, #1c2128 0%, #21262d 100%);
  border-left: 4px solid #58a6ff;
  border-radius: 8px;
  padding: 16px 20px;
  margin: 20px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
&#10;.info-box strong {
  color: #58a6ff;
}
&#10;h1 {
  color: #58a6ff;
  font-size: 2.5rem;
  font-weight: 700;
}
&#10;h2 {
  color: #f0f6fc;
  font-size: 1.8rem;
  font-weight: 600;
  border-bottom: 2px solid #21262d;
  padding-bottom: 10px;
  margin-top: 40px;
  margin-bottom: 20px;
}
&#10;h3 {
  color: #58a6ff;
  font-size: 1.4rem;
  font-weight: 600;
  margin-top: 30px;
  margin-bottom: 15px;
}
&#10;h4 {
  color: #7c3aed;
  font-size: 1.2rem;
  font-weight: 600;
  margin-top: 25px;
  margin-bottom: 12px;
}
&#10;.accent-text {
  color: #7c3aed;
  font-weight: 600;
}
&#10;.highlight-text {
  color: #ff7b72;
  font-weight: 500;
}
&#10;.success-text {
  color: #3fb950;
  font-weight: 500;
}
&#10;.warning-text {
  color: #d29922;
  font-weight: 500;
}
&#10;code {
  background: #21262d;
  color: #79c0ff;
  padding: 3px 6px;
  border-radius: 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
}
&#10;pre {
  background: #0d1117;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 20px;
  overflow-x: auto;
  margin: 15px 0;
}
&#10;pre code {
  background: transparent;
  color: #e6edf3;
  padding: 0;
}
&#10;blockquote {
  border-left: 4px solid #30363d;
  padding-left: 16px;
  margin: 20px 0;
  color: #8b949e;
  font-style: italic;
}
&#10;strong {
  color: #f0f6fc;
  font-weight: 600;
}
&#10;a {
  color: #58a6ff;
  text-decoration: none;
}
&#10;a:hover {
  text-decoration: underline;
}
&#10;table {
  border-collapse: collapse;
  width: 100%;
  margin: 20px 0;
}
&#10;td, th {
  border: 1px solid #30363d;
  padding: 12px 16px;
  text-align: left;
}
&#10;th {
  background: #21262d;
  color: #58a6ff;
  font-weight: 600;
}
&#10;tr:nth-child(even) {
  background: rgba(22, 27, 34, 0.5);
}
</style>

<div class="main-header">

<!-- Language Toggle -->

<div class="language-toggle">

    <a href="#english">🇺🇸 English</a>
    <span style="color: #6e7681;">|</span>
    <a href="#中文">🇨🇳 中文</a>

</div>

<!-- Title Section -->

<div class="title-section">

    <h1 class="main-title">🧪 labtools</h1>

    <div class="r-badge">
      <img src="https://img.shields.io/badge/R-276DC3?style=for-the-badge&logo=r&logoColor=white" alt="R" height="32"/>
    </div>

    <h2 class="subtitle">⚗️ Analytical Chemistry Laboratory Data Processing Toolkit</h2>

    <p class="description">
      A comprehensive R package for streamlining analytical chemistry workflows
    </p>

    <!-- Badges -->
    <div class="badges-container">
      <img src="https://img.shields.io/github/workflow/status/QizhiSu/labtools/R-CMD-check?style=flat-square&logo=github&logoColor=white&labelColor=21262d&color=3fb950" alt="R-CMD-check"/>
      <img src="https://img.shields.io/badge/License-MIT-7c3aed?style=flat-square&labelColor=21262d" alt="License"/>
      <img src="https://img.shields.io/badge/CRAN-not%20yet-ff7b72?style=flat-square&labelColor=21262d" alt="CRAN"/>
      <img src="https://img.shields.io/github/stars/QizhiSu/labtools?style=flat-square&logo=github&logoColor=white&labelColor=21262d&color=d29922" alt="Stars"/>
      <img src="https://img.shields.io/github/issues/QizhiSu/labtools?style=flat-square&logo=github&logoColor=white&labelColor=21262d&color=58a6ff" alt="Issues"/>
      <img src="https://img.shields.io/github/last-commit/QizhiSu/labtools?style=flat-square&logo=github&logoColor=white&labelColor=21262d&color=7c3aed" alt="Last Commit"/>
    </div>

</div>

</div>

------------------------------------------------------------------------

## <a id="english"></a>🌟 **Key Features**

<div class="feature-table">

| Feature                              | Description                                                                 |
|--------------------------------------|-----------------------------------------------------------------------------|
| 🔍 **Chemical Metadata Extraction**  | Automated retrieval from PubChem database with intelligent retry mechanisms |
| 🧬 **Structure Database Management** | Build and visualize chemical structure databases with advanced filtering    |
| 📊 **MS Data Processing**            | Export optimized databases for MS-FINDER and MS-DIAL software               |
| 🔬 **2D GC-MS Analysis**             | Process Canvas exports and combine multi-sample data with precision         |
| ⚗️ **Chemical Structure Conversion** | SMILES to MOL file conversion with 2D coordinate generation                 |
| 📈 **Semi-quantification Tools**     | Advanced analytical quantification with machine learning integration        |
| 🎯 **Interactive Visualization**     | Shiny-based chemical structure navigation and spectrum plotting             |

</div>

<div class="info-box">

<strong>💡 New to R?</strong> For detailed help on any function, use
<code>?function_name</code> in R console (e.g.,
<code>?extract_cid</code>)

</div>

------------------------------------------------------------------------

## 🚀 **Installation**

<div class="code-block">

### Development Version (Recommended)

``` r
# Install devtools if you haven't already
if (!requireNamespace("devtools", quietly = TRUE)) {
  install.packages("devtools")
}

# Install labtools from GitHub
devtools::install_github("QizhiSu/labtools")
```

### System Requirements

- **R** ≥ 4.0.0
- **Java** ≥ 8 (for rcdk package)
- **Internet connection** (for PubChem API access)

### Getting Help

- **Function help**: Use `?function_name` (e.g., `?extract_cid`)
- **Package help**: Use `help(package = "labtools")`
- **Examples**: Use `example(function_name)` to run examples

</div>

------------------------------------------------------------------------

## ⚡ **Quick Start**

<div class="code-block">

``` r
library(labtools)
library(dplyr)

# 🔍 1. Extract chemical metadata from PubChem
data <- data.frame(
  Name = c("Caffeine", "Aspirin", "Glucose"),
  CAS = c("58-08-2", "50-78-2", "50-99-7")
)

# Extract CIDs and comprehensive metadata
data_with_cid <- extract_cid(data, name_col = "Name", cas_col = "CAS")
data_complete <- extract_meta(data_with_cid, cas = TRUE, flavornet = TRUE)

# 📊 2. Export for MS software
export4msdial(data_complete, polarity = "pos", output = "database_msdial.txt")
export4msfinder(data_complete, output = "database_msfinder.txt")

# ⚗️ 3. Convert SMILES to MOL files
smiles_data <- data.frame(
  ID = c("Caffeine", "Aspirin"),
  SMILES = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O")
)
export_smiles_to_mol(smiles_data, output_dir = "mol_files")

# 🎯 4. Interactive chemical structure browser
navigate_chem(data_complete)  # Opens Shiny app
```

<div class="info-box">

<strong>💡 Tip:</strong> Use <code>?extract_cid</code> or
<code>?export_smiles_to_mol</code> to see detailed parameter
descriptions and more examples!

</div>

</div>

------------------------------------------------------------------------

## 📚 **Detailed Function Examples**

### 🔍 Chemical Metadata Extraction

<div class="code-block">

``` r
library(labtools)

# Extract CIDs from chemical identifiers
compounds <- data.frame(
  Name = c("Caffeine", "Theobromine"),
  CAS = c("58-08-2", "83-67-0")
)

# Extract CIDs with proper error handling
# Use ?extract_cid for full parameter details
compounds_cid <- extract_cid(
  data = compounds,           # Input data frame
  name_col = "Name",         # Column with chemical names (default: "Name")
  cas_col = "CAS",           # Column with CAS numbers (default: "CAS")
  inchikey_col = "InChIKey", # Column with InChIKeys (default: "InChIKey")
  timeout = 300,             # API timeout in seconds (default: 180)
  verbose = TRUE,            # Show progress messages (default: TRUE)
  checkpoint_file = "cid_checkpoint.rds",  # Checkpoint file (default)
  use_checkpoint = TRUE      # Use checkpoint functionality (default: TRUE)
)

# Extract comprehensive metadata
# Use ?extract_meta for all options
compounds_meta <- extract_meta(
  data = compounds_cid,      # Data frame with CID column (required)
  cas = TRUE,                # Extract CAS numbers (default: FALSE)
  flavornet = TRUE,          # Extract Flavornet data (default: FALSE)
  synonyms = TRUE,           # Extract synonyms (default: FALSE)
  verbose = TRUE,            # Show progress (default: TRUE)
  checkpoint_dir = "."       # Checkpoint directory (default: ".")
)

# Add chemical classification using ClassyFire
# Use ?extract_classyfire for details
compounds_classified <- extract_classyfire(
  data = compounds_meta,     # Data frame with InChIKey column
  inchikey_col = "InChIKey", # InChIKey column name (default)
  name_col = "Name"          # Name column for progress (default)
)
```

**Key Parameters Explained:** - `name_col`, `cas_col`, `inchikey_col`:
Specify which columns contain identifiers (defaults: “Name”, “CAS”,
“InChIKey”) - `timeout`: How long to wait for PubChem API responses
(default: 180 seconds, increase for slow connections) - `verbose`: Set
to `FALSE` to suppress progress messages (default: TRUE) -
`checkpoint_file`: Saves progress to resume interrupted extractions
(default: “cid_checkpoint.rds”) - `use_checkpoint`: Enable/disable
checkpoint functionality (default: TRUE)

</div>

### 📤 Database Export

<div class="code-block">

``` r
# Export for MS-FINDER software
# Use ?export4msfinder for details
export4msfinder(
  data = compounds_classified,              # Data with required columns
  output = "msfinder_database.txt"          # Output file path (default)
)

# Export for MS-DIAL (positive mode)
# Use ?export4msdial for all options
export4msdial(
  data = compounds_classified,              # Data with required columns
  polarity = "pos",                         # ESI polarity: "pos" or "neg" (default: "pos")
  output = "msdial_pos.txt"                # Output file path (default)
)

# Export for MS-DIAL (negative mode)
export4msdial(
  data = compounds_classified,
  polarity = "neg",                         # Negative mode for different adducts
  output = "msdial_neg.txt"
)
```

**Required Columns:** - **MS-FINDER**: Name, InChIKey, CID, ExactMass,
Formula, SMILES - **MS-DIAL**: Name, ExactMass, SMILES, InChIKey

**Polarity Options:** - `"pos"`: Generates \[M+H\]+, \[M+Na\]+, \[M+K\]+
adducts - `"neg"`: Generates \[M-H\]-, \[M+Cl\]-, \[M+HCOO\]- adducts

</div>

### ⚗️ SMILES to MOL Conversion

<div class="code-block">

``` r
# Prepare SMILES data
smiles_data <- data.frame(
  Compound_ID = c("CAFF_001", "THEO_002"),
  SMILES_String = c(
    "Cn1cnc2c1c(=O)n(c(=O)n2C)C",      # Caffeine
    "Cn1cnc2c1c(=O)[nH]c(=O)n2C"       # Theobromine
  ),
  stringsAsFactors = FALSE
)

# Convert to MOL files with 2D coordinates
# Use ?export_smiles_to_mol for all parameters
result <- export_smiles_to_mol(
  df = smiles_data,                     # Data frame with ID and SMILES columns
  id_col = "Compound_ID",               # Column with compound IDs (default: "ID")
  smiles_col = "SMILES_String",         # Column with SMILES strings (default: "SMILES")
  output_dir = "molecular_structures"   # Output directory (default: "mol_files")
)

# Check conversion results
print(result)
# Returns: list(success = 2, failed = 0, skipped = 0)
```

**Function Features:** - **Input validation**: Checks for valid data
frame and required columns - **Error handling**: Continues processing
even if some SMILES fail - **2D coordinates**: Automatically generates
2D coordinates for visualization - **Progress tracking**: Returns
summary of successful/failed conversions - **Robust processing**:
Handles NA/NULL values gracefully

</div>

### Canvas Data Processing

``` r
# Process Canvas 2D GC-MS data
canvas_data <- read_canvas(
  path = "path/to/canvas/files",
  ri_align_tol = 50,
  rt_2d_align_tol = 0.1,
  keep = "area"
)

# Normalize by internal standard
normalized_data <- normalize_area(canvas_data, start_col = 12)
```

### Semi-quantification Analysis

``` r
# Select appropriate standards
standards_assigned <- select_std(
  std_md = reference_standards,
  std_res_col = 3,
  std_md1_col = 14,
  data_md = target_compounds,
  data_md1_col = 5,
  top_npct_md = 30
)

# Calculate concentrations
concentrations <- calculate_con(standards_assigned, sample_weights)

# Organize results
final_results <- organize_con(concentrations, sample_codes, digits = 3)
```

### Spectral Analysis

``` r
# Convert spectrum string to numeric vector
spectrum_str <- "100:30 120:80 145:60 170:90 200:20"
spectrum <- update_spectrum(spectrum_str)

# Create interactive spectrum plot
plot_spectrum(spectrum, range = 10, threshold = 5)

# Calculate spectral similarity
spec1 <- update_spectrum("100:30 120:80 145:60")
spec2 <- update_spectrum("100:25 120:85 145:55")
similarity <- cosine_similarity(spec1, spec2)
```

### MSP Library Filtering

``` r
# Filter MSP library based on compound list
compounds_to_keep <- data.frame(
  Name = c("Caffeine", "Aspirin"),
  InChIKey = c("RYYVLZVUVIJVGH-UHFFFAOYSA-N", "BSYNRYMUTXBXSQ-UHFFFAOYSA-N")
)

filter_msp(msp = "NIST_library.msp",
          cmp_list = compounds_to_keep,
          keep_napd8 = TRUE,
          output = "filtered_library.msp")
```

------------------------------------------------------------------------

## 🔬 **Advanced Workflows**

### Complete Database Creation Workflow

``` r
# Step 1: Prepare data
chemicals <- data.frame(
  Name = c("Benzene", "Toluene", "Xylene"),
  CAS = c("71-43-2", "108-88-3", "1330-20-7")
)

# Step 2: Extract comprehensive metadata
chemicals_cid <- extract_cid(chemicals, name_col = "Name", cas_col = "CAS")
chemicals_meta <- extract_meta(chemicals_cid, cas = TRUE, synonyms = TRUE)
chemicals_class <- extract_classyfire(chemicals_meta)

# Step 3: Export for different platforms
export4msdial(chemicals_class, polarity = "pos", output = "pos_database.txt")
export4msdial(chemicals_class, polarity = "neg", output = "neg_database.txt")
export4msfinder(chemicals_class, output = "msfinder_database.txt")

# Step 4: Generate structure files
export_smiles_to_mol(chemicals_class, output_dir = "structures")
```

### 2D GC-MS Processing Pipeline

``` r
# Process Canvas data
canvas_data <- read_canvas("canvas_files", keep = "area")

# Extract metadata
canvas_meta <- extract_cid(canvas_data, name_col = "Name", cas_col = "CAS")
canvas_complete <- extract_meta(canvas_meta, cas = TRUE)

# Normalize and analyze
canvas_normalized <- normalize_area(canvas_complete)
canvas_filtered <- keep_area(canvas_normalized, sample_codes)

# Semi-quantification
standards <- select_std(ref_standards, 3, 14, canvas_filtered, 5)
concentrations <- calculate_con(standards, sample_weights)
results <- organize_con(concentrations, sample_codes)
```

------------------------------------------------------------------------

## <a id="中文"></a>🧪 **labtools**: 分析化学实验室数据处理工具包

> **专为分析化学工作流程设计的综合性R包**

<div class="feature-table">

### 🌟 **主要功能**

| 功能                         | 描述                                      |
|------------------------------|-------------------------------------------|
| 🔍 **化学元数据提取**        | 从PubChem数据库自动检索，具有智能重试机制 |
| 🧬 **结构数据库管理**        | 构建和可视化化学结构数据库，支持高级过滤  |
| 📊 **质谱数据处理**          | 为MS-FINDER和MS-DIAL软件导出优化数据库    |
| 🔬 **二维气相色谱-质谱分析** | 精确处理Canvas导出数据并合并多样本数据    |
| ⚗️ **化学结构转换**          | SMILES到MOL文件转换，支持2D坐标生成       |
| 📈 **半定量工具**            | 集成机器学习的高级分析定量方法            |
| 🎯 **交互式可视化**          | 基于Shiny的化学结构导航和光谱绘图         |

</div>

<div class="info-box">

<strong>💡 R语言新手？</strong>
对于任何函数的详细帮助，请在R控制台中使用 <code>?函数名</code>
（例如：<code>?extract_cid</code>）

</div>

### 🚀 **安装**

<div class="code-block">

``` r
# 安装devtools（如果尚未安装）
if (!requireNamespace("devtools", quietly = TRUE)) {
  install.packages("devtools")
}

# 从GitHub安装labtools
devtools::install_github("QizhiSu/labtools")
```

### 系统要求

- **R** ≥ 4.0.0
- **Java** ≥ 8 (rcdk包需要)
- **网络连接** (访问PubChem API)

### 获取帮助

- **函数帮助**: 使用 `?函数名` (例如：`?extract_cid`)
- **包帮助**: 使用 `help(package = "labtools")`
- **示例**: 使用 `example(函数名)` 运行示例

</div>

### ⚡ **快速开始**

<div class="code-block">

``` r
library(labtools)
library(dplyr)

# 🔍 1. 从PubChem提取化学元数据
data <- data.frame(
  Name = c("咖啡因", "阿司匹林", "葡萄糖"),
  CAS = c("58-08-2", "50-78-2", "50-99-7")
)

# 提取CID和综合元数据
data_with_cid <- extract_cid(data, name_col = "Name", cas_col = "CAS")
data_complete <- extract_meta(data_with_cid, cas = TRUE, flavornet = TRUE)

# 📊 2. 导出用于质谱软件
export4msdial(data_complete, polarity = "pos", output = "database_msdial.txt")
export4msfinder(data_complete, output = "database_msfinder.txt")

# ⚗️ 3. 转换SMILES为MOL文件
smiles_data <- data.frame(
  ID = c("咖啡因", "阿司匹林"),
  SMILES = c("Cn1cnc2c1c(=O)n(c(=O)n2C)C", "CC(=O)OC1=CC=CC=C1C(=O)O")
)
export_smiles_to_mol(smiles_data, output_dir = "mol_files")

# 🎯 4. 交互式化学结构浏览器
navigate_chem(data_complete)  # 打开Shiny应用
```

<div class="info-box">

<strong>💡 提示:</strong> 使用 <code>?extract_cid</code> 或
<code>?export_smiles_to_mol</code> 查看详细的参数说明和更多示例！

</div>

</div>

### 📚 **详细函数示例**

#### 🔍 化学元数据提取

<div class="code-block">

``` r
library(labtools)

# 从化学标识符提取CID
compounds <- data.frame(
  Name = c("咖啡因", "可可碱"),
  CAS = c("58-08-2", "83-67-0")
)

# 提取CID并进行适当的错误处理
# 使用 ?extract_cid 查看完整参数详情
compounds_cid <- extract_cid(
  data = compounds,           # 输入数据框
  name_col = "Name",         # 包含化学名称的列 (默认: "Name")
  cas_col = "CAS",           # 包含CAS号的列 (默认: "CAS")
  inchikey_col = "InChIKey", # 包含InChIKey的列 (默认: "InChIKey")
  timeout = 300,             # API超时时间(秒) (默认: 180)
  verbose = TRUE,            # 显示进度信息 (默认: TRUE)
  checkpoint_file = "cid_checkpoint.rds",  # 检查点文件 (默认值)
  use_checkpoint = TRUE      # 使用检查点功能 (默认: TRUE)
)

# 提取综合元数据
# 使用 ?extract_meta 查看所有选项
compounds_meta <- extract_meta(
  data = compounds_cid,      # 包含CID列的数据框 (必需)
  cas = TRUE,                # 提取CAS号 (默认: FALSE)
  flavornet = TRUE,          # 提取Flavornet数据 (默认: FALSE)
  synonyms = TRUE,           # 提取同义词 (默认: FALSE)
  verbose = TRUE,            # 显示进度 (默认: TRUE)
  checkpoint_dir = "."       # 检查点目录 (默认: ".")
)

# 使用ClassyFire添加化学分类
# 使用 ?extract_classyfire 查看详情
compounds_classified <- extract_classyfire(
  data = compounds_meta,     # 包含InChIKey列的数据框
  inchikey_col = "InChIKey", # InChIKey列名 (默认值)
  name_col = "Name"          # 用于进度显示的名称列 (默认值)
)
```

**关键参数说明:** - `name_col`, `cas_col`, `inchikey_col`:
指定包含标识符的列 (默认值: “Name”, “CAS”, “InChIKey”) - `timeout`:
等待PubChem API响应的时间 (默认: 180秒，网络慢时可增加) - `verbose`:
设为 `FALSE` 可抑制进度信息 (默认: TRUE) - `checkpoint_file`:
保存进度以便恢复中断的提取 (默认: “cid_checkpoint.rds”) -
`use_checkpoint`: 启用/禁用检查点功能 (默认: TRUE)

</div>

------------------------------------------------------------------------

## 📄 **许可证**

本项目采用MIT许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。

------------------------------------------------------------------------

## 👨‍🔬 **作者信息**

**苏启枝 (Qizhi Su)** - *包开发者* - 📧 邮箱: <<EMAIL>> - 🆔
ORCID: [0000-0002-8124-997X](https://orcid.org/0000-0002-8124-997X) - 🐙
GitHub: [@QizhiSu](https://github.com/QizhiSu)

------------------------------------------------------------------------

<div align="center">

**⭐ 如果您觉得labtools有用，请考虑给它一个星标！⭐**

[![GitHub
stars](https://img.shields.io/github/stars/QizhiSu/labtools?style=for-the-badge&logo=github&logoColor=white&labelColor=1a1a1a&color=ffd93d)](https://github.com/QizhiSu/labtools/stargazers)

------------------------------------------------------------------------

<h3 style="color: #00d4aa;">
🔬 让分析化学数据处理变得简单高效 🔬
</h3>
<p style="color: #a0a0a0; font-style: italic;">
Streamlining analytical chemistry workflows with precision and elegance
</p>

</div>
